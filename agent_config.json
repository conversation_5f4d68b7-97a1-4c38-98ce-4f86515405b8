{
  "name": "Untitled_Workflow",
  "description": "Untitled_Workflow",
  "workflow_data": {
    "nodes": [
      {
        "id": "start-node",
        "type": "WorkflowNode",
        "position": {
          "x": 100,
          "y": 100
        },
        "data": {
          "label": "Start",
          "type": "component",
          "originalType": "StartNode",
          "definition": {
            "name": "StartNode",
            "display_name": "Start",
            "description": "The starting point for all workflows. Only nodes connected to this node will be executed.",
            "category": "Input/Output",
            "icon": "Play",
            "beta": false,
            "inputs": [],
            "outputs": [
              {
                "name": "flow",
                "display_name": "Flow",
                "output_type": "Any"
              }
            ],
            "is_valid": true,
            "path": "components.io.start_node"
          },
          "config": {
            "collected_parameters": {
              "AgenticAI-1748847020889_objective": {
                "node_id": "AgenticAI-1748847020889",
                "node_name": "AI Agent Executor",
                "input_name": "objective",
                "connected_to_start": true,
                "required": true,
                "input_type": "string",
                "options": null
              }
            }
          }
        },
        "width": 208,
        "height": 171,
        "selected": false,
        "dragging": false
      },
      {
        "id": "AgenticAI-1748847020889",
        "type": "WorkflowNode",
        "position": {
          "x": 460,
          "y": 40
        },
        "data": {
          "label": "AI Agent Executor",
          "type": "agent",
          "originalType": "AgenticAI",
          "agent_type":
          
          "definition": {
            "name": "AgenticAI",
            "display_name": "AI Agent Executor",
            "description": "Executes an AI agent with tools and memory using AutoGen.",
            "category": "AI",
            "icon": "Bot",
            "beta": true,
            "requires_approval": false,
            "inputs": [
                {executor type}
                {termination condition}
                { "max_tokens":}
              {
                "name": "model_provider",
                "display_name": "Model Provider",
                "info": "The AI model provider to use.",
                "input_type": "dropdown",
                "input_types": null,
                "required": false,
                "is_handle": false,
                "is_list": false,
                "real_time_refresh": false,
                "advanced": false,
                "value": "OpenAI",
                "options": [
                  "OpenAI",
                  "Azure OpenAI",
                  "Anthropic",
                  "Claude",
                  "Google",
                  "Gemini",
                  "Mistral",
                  "Ollama",
                  "Custom"
                ],
                "visibility_rules": null,
                "visibility_logic": "OR",
                "requirement_rules": null,
                "requirement_logic": "OR"
              },
              {
                "name": "base_url",
                "display_name": "Base URL",
                "info": "Base URL for the API (leave empty for default provider URL).",
                "input_type": "string",
                "input_types": null,
                "required": false,
                "is_handle": false,
                "is_list": false,
                "real_time_refresh": false,
                "advanced": false,
                "value": "",
                "options": null,
                "visibility_rules": [
                  {
                    "field_name": "model_provider",
                    "field_value": "Custom",
                    "operator": "equals"
                  },
                  {
                    "field_name": "model_provider",
                    "field_value": "Azure OpenAI",
                    "operator": "equals"
                  },
                  {
                    "field_name": "model_provider",
                    "field_value": "Ollama",
                    "operator": "equals"
                  }
                ],
                "visibility_logic": "OR",
                "requirement_rules": null,
                "requirement_logic": "OR"
              },
              {
                "name": "api_key",
                "display_name": "API Key",
                "info": "API key for the model provider. Can be entered directly or referenced from secure storage.",
                "input_type": "credential",
                "input_types": null,
                "required": false,
                "is_handle": false,
                "is_list": false,
                "real_time_refresh": false,
                "advanced": false,
                "value": "",
                "options": null,
                "visibility_rules": null,
                "visibility_logic": "OR",
                "requirement_rules": null,
                "requirement_logic": "OR",
                "credential_type": "api_key",
                "use_credential_id": false,
                "credential_id": ""
              },
              {
                "name": "model_name",
                "display_name": "Model",
                "info": "Select the model to use.",
                "input_type": "dropdown",
                "input_types": null,
                "required": false,
                "is_handle": false,
                "is_list": false,
                "real_time_refresh": false,
                "advanced": false,
                "value": "",
                "options": null,
                "visibility_rules": null,
                "visibility_logic": "OR",
                "requirement_rules": null,
                "requirement_logic": "OR"
              },
              {
                "name": "temperature",
                "display_name": "Temperature",
                "info": "Controls randomness: 0 is deterministic, higher values are more random.",
                "input_type": "float",
                "input_types": null,
                "required": false,
                "is_handle": false,
                "is_list": false,
                "real_time_refresh": false,
                "advanced": false,
                "value": 0.7,
                "options": null,
                "visibility_rules": null,
                "visibility_logic": "OR",
                "requirement_rules": null,
                "requirement_logic": "OR"
              },
              {
                "name": "objective",
                "display_name": "Objective",
                "info": "The task or objective for the agent to accomplish. Can be connected from another node or entered directly.",
                "input_type": "string",
                "input_types": [
                  "string",
                  "Any"
                ],
                "required": true,
                "is_handle": true,
                "is_list": false,
                "real_time_refresh": false,
                "advanced": false,
                "value": "",
                "options": null,
                "visibility_rules": null,
                "visibility_logic": "OR",
                "requirement_rules": null,
                "requirement_logic": "OR"
              },
              {
                "name": "input_variables",
                "display_name": "Input Variables",
                "info": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.",
                "input_type": "dict",
                "input_types": [
                  "dict",
                  "Any"
                ],
                "required": false,
                "is_handle": true,
                "is_list": false,
                "real_time_refresh": false,
                "advanced": false,
                "value": {},
                "options": null,
                "visibility_rules": null,
                "visibility_logic": "OR",
                "requirement_rules": null,
                "requirement_logic": "OR"
              },
              {
                "name": "tools",
                "display_name": "Tools",
                "info": "List of tools available to the agent. Can be connected from another node or entered directly.",
                "input_type": "list",
                "input_types": [
                  "list",
                  "Any"
                ],
                "required": false,
                "is_handle": true,
                "is_list": true,
                "real_time_refresh": false,
                "advanced": false,
                "value": [],
                "options": null,
                "visibility_rules": null,
                "visibility_logic": "OR",
                "requirement_rules": null,
                "requirement_logic": "OR"
              },
              {
                "name": "memory",
                "display_name": "Memory Object",
                "info": "Connect a memory object from another node.",
                "input_type": "handle",
                "input_types": [
                  "Any"
                ],
                "required": false,
                "is_handle": true,
                "is_list": false,
                "real_time_refresh": false,
                "advanced": false,
                "value": null,
                "options": null,
                "visibility_rules": null,
                "visibility_logic": "OR",
                "requirement_rules": null,
                "requirement_logic": "OR"
              },
              {
                "name": "agent_type",
                "display_name": "Agent Type",
                "info": "The type of agent to create.",
                "input_type": "dropdown",
                "input_types": null,
                "required": false,
                "is_handle": false,
                "is_list": false,
                "real_time_refresh": false,
                "advanced": false,
                "value": "Assistant",
                "options": [
                  "Assistant",
                  "UserProxy",
                  "CodeExecutor"
                ],
                "visibility_rules": null,
                "visibility_logic": "OR",
                "requirement_rules": null,
                "requirement_logic": "OR"
              },
              {
                "name": "stream",
                "display_name": "Stream Response",
                "info": "Enable streaming for real-time responses.",
                "input_type": "bool",
                "input_types": null,
                "required": false,
                "is_handle": false,
                "is_list": false,
                "real_time_refresh": false,
                "advanced": false,
                "value": false,
                "options": null,
                "visibility_rules": null,
                "visibility_logic": "OR",
                "requirement_rules": null,
                "requirement_logic": "OR"
              }
            ],
            "outputs": [
              {
                "name": "final_answer",
                "display_name": "Final Answer",
                "output_type": "string",
                "semantic_type": null,
                "method": null
              },
              {
                "name": "intermediate_steps",
                "display_name": "Intermediate Steps",
                "output_type": "list",
                "semantic_type": null,
                "method": null
              },
              {
                "name": "updated_memory",
                "display_name": "Updated Memory",
                "output_type": "Memory",
                "semantic_type": null,
                "method": null
              },
              {
                "name": "error",
                "display_name": "Error",
                "output_type": "str",
                "semantic_type": null,
                "method": null
              }
            ],
            "is_valid": true,
            "path": "components.ai.agenticai",
            "interface_issues": []
          },
          "config": {}
        },
        "style": {
          "opacity": 1
        },
        "width": 210,
        "height": 339,
        "selected": false,
        "positionAbsolute": {
          "x": 460,
          "y": 40
        },
        "dragging": false
      }
    ],
    "edges": [
      {
        "animated": true,
        "style": {
          "strokeWidth": 2,
          "stroke": "var(--primary)",
          "zIndex": 5
        },
        "source": "start-node",
        "sourceHandle": "flow",
        "target": "AgenticAI-1748847020889",
        "targetHandle": "objective",
        "type": "default",
        "id": "reactflow__edge-start-nodeflow-AgenticAI-1748847020889objective"
      }
    ]
  },
  "start_node_data": [
    {
      "field": "objective",
      "type": "string",
      "transition_id": "AgenticAI-1748847020889"
    }
  ]
}