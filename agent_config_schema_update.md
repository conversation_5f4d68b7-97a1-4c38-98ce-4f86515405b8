# Agent Configuration Schema Update

## Overview
Updated the AgenticAI component in the workflow builder to support the orchestration engine's specific JSON format requirements while maintaining backward compatibility.

## Updated Schema Structure

### User-Configurable Inputs (Exposed in Workflow Builder UI)

#### Agent Identification & Configuration
- **`id`**: string (optional, auto-generated if empty)
  - Display Name: "Agent ID"
  - Info: "Unique identifier for the agent. If empty, will be auto-generated."

- **`name`**: string (default: "AI Agent")
  - Display Name: "Agent Name"
  - Info: "Display name for the agent."

- **`description`**: string (default: "")
  - Display Name: "Description"
  - Info: "Description of the agent for UI display."

#### Execution Configuration
- **`execution_type`**: enum ["response", "interactive"] (default: "response")
  - Display Name: "Execution Type"
  - Info: "Determines if agent handles single response or multi-turn conversation."

- **`query`**: string (required, dual-purpose input)
  - Display Name: "Query/Objective"
  - Info: "The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly."

- **`system_message`**: string (default: "")
  - Display Name: "System Message"
  - Info: "System prompt/instructions for the agent. If empty, will use default based on query."

- **`termination_condition`**: string (conditionally required)
  - Display Name: "Termination Condition"
  - Info: "Defines when multi-turn conversations should end. Required for interactive execution type."
  - Visibility: Only shown when `execution_type` is "interactive"

#### Model Configuration
- **`max_tokens`**: number (default: 1000)
  - Display Name: "Max Tokens"
  - Info: "Maximum response length in tokens."

- **Inherited from BaseAgentComponent**:
  - `model_provider`: dropdown (OpenAI, Azure OpenAI, Anthropic, etc.)
  - `base_url`: string (conditional visibility)
  - `api_key`: credential input
  - `model_name`: dropdown (provider-specific)
  - `temperature`: float (default: 0.7)

#### Data & Tools
- **`input_variables`**: dict (dual-purpose input, default: {})
  - Display Name: "Input Variables"
  - Info: "Dictionary of variables to provide to the agent."

- **`tools`**: list (dual-purpose input, default: [])
  - Display Name: "Tools"
  - Info: "List of tools available to the agent."

- **`memory`**: handle input (connection only)
  - Display Name: "Memory Object"
  - Info: "Connect a memory object from another node."

#### Advanced Configuration
- **`autogen_agent_type`**: enum ["Assistant", "UserProxy", "CodeExecutor"] (default: "Assistant")
  - Display Name: "AutoGen Agent Type"
  - Info: "The type of AutoGen agent to create internally."
  - Advanced: true

- **`stream`**: boolean (default: false)
  - Display Name: "Stream Response"
  - Info: "Enable streaming for real-time responses."

### Hardcoded Values (Not User-Configurable)

- **`agent_type`**: Always "component" (for orchestration engine compatibility)
  - Implemented as a property method that returns "component"

## Orchestration Engine Configuration Format

The component now provides a `get_agent_config()` method that generates the required JSON format:

```json
{
  "id": "agent_<node_id>",
  "name": "AI Agent",
  "description": "",
  "agent_type": "component",
  "execution_type": "response",
  "query": "User's query/objective",
  "system_message": "System prompt",
  "tools": [],
  "agent_config": {
    "model_provider": "OpenAI",
    "model_name": "gpt-4-turbo",
    "temperature": 0.7,
    "max_tokens": 1000
  },
  "termination_condition": "Only present for interactive agents"
}
```

## Validation Rules

1. **Required Fields**: `query` is always required
2. **Conditional Requirements**: `termination_condition` is required when `execution_type` is "interactive"
3. **Visibility Rules**: `termination_condition` field is only visible when `execution_type` is "interactive"

## Backward Compatibility

- Existing workflows using the old `objective` field will need to be migrated to use `query`
- All other existing functionality is preserved
- Legacy `build` method is maintained with deprecation warning

## Implementation Details

### Key Changes Made:
1. Added new input fields with proper validation and visibility rules
2. Replaced `objective` with `query` for consistency with orchestration engine
3. Added `agent_type` property that returns hardcoded "component" value
4. Added `get_agent_config()` method for orchestration engine integration
5. Updated execute method to handle all new input fields
6. Maintained dual-purpose input pattern for flexible connectivity

### Files Modified:
- `workflow-service/app/components/ai/agentic_ai.py`

### Testing Recommendations:
1. Test component creation with new schema
2. Verify conditional field visibility (termination_condition)
3. Test agent configuration generation for orchestration engine
4. Verify backward compatibility with existing workflows
5. Test both response and interactive execution types
