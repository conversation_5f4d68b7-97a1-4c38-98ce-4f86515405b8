2025-06-02 19:18:55 - ComponentSystem - INFO - [setup_logger:467] Logger ComponentSystem configured with log file: logs\2025-06-02\ComponentSystem_19-18-55.log
2025-06-02 19:18:55 - ComponentSystem - INFO - [get_component_manager:1393] Creating new global ComponentManager instance
2025-06-02 19:18:55 - ComponentSystem - INFO - [__init__:87] Initializing ComponentManager with Kafka bootstrap servers: **************:9092
2025-06-02 19:18:55 - ComponentSystem - INFO - [__init__:92] Kafka Consumer Topic: node-execution-request
2025-06-02 19:18:55 - ComponentSystem - INFO - [__init__:93] Kafka Results Topic: node_results
2025-06-02 19:18:55 - ComponentSystem - INFO - [__init__:94] Kafka Consumer Group ID: node_executor_service
2025-06-02 19:18:55 - ComponentSystem - INFO - [__init__:95] Kafka Producer Request Timeout: 60000ms
2025-06-02 19:18:55 - ComponentSystem - INFO - [__init__:98] Kafka Consumer Fetch Min Bytes: 100
2025-06-02 19:18:55 - ComponentSystem - INFO - [__init__:101] Kafka Consumer Fetch Max Wait: 500ms
2025-06-02 19:18:55 - ComponentSystem - INFO - [__init__:104] Kafka Consumer Session Timeout: 10000ms
2025-06-02 19:18:55 - ComponentSystem - INFO - [__init__:107] Kafka Consumer Heartbeat Interval: 3000ms
2025-06-02 19:18:55 - ComponentSystem - INFO - [__init__:110] Default Node Retries: 3
2025-06-02 19:18:55 - ComponentSystem - INFO - [__init__:128] Initializing ThreadPoolExecutor with 4 workers
2025-06-02 19:18:55 - ComponentSystem - INFO - [__init__:132] ThreadPoolExecutor initialized
2025-06-02 19:18:55 - ComponentSystem - INFO - [discover_components:141] Discovering components
2025-06-02 19:18:55 - ComponentSystem - INFO - [discover_components:142] Component registry before discovery: []
2025-06-02 19:18:55 - ComponentSystem - INFO - [discover_components:153] Discovered components: []
2025-06-02 19:18:55 - ComponentSystem - INFO - [discover_component_modules:1332] Discovering component modules
2025-06-02 19:18:55 - ComponentSystem - INFO - [discover_component_modules:1348] Found 16 potential component files: ['alter_metadata_component.py', 'api_component.py', 'combine_text_component.py', 'combine_text_component_new.py', 'convert_script_data_component.py', 'data_to_dataframe_component.py', 'doc_component.py', 'dynamic_combine_text_component.py', 'gmail_component.py', 'gmail_tracker_component.py', 'id_generator_component.py', 'merge_data_component.py', 'message_to_data_component.py', 'select_data_component.py', 'split_text_component.py', 'text_analysis_component.py']
2025-06-02 19:18:55 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.alter_metadata_component
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:63] Registering component: ApiRequestNode -> ApiComponent
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode']
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:63] Registering component: CombineTextComponent -> CombineTextComponent
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent']
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:63] Registering component: combine_text -> CombineTextComponent
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text']
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:63] Registering component: DocComponent -> DocComponent
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent']
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:63] Registering component: SelectDataComponent -> SelectDataExecutor
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent']
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:63] Registering component: SplitTextComponent -> SplitTextComponent
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent']
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:63] Registering component: text_analysis -> TextAnalysisComponent
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis']
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:63] Registering component: AlterMetadataComponent -> AlterMetadataComponent
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent']
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:63] Registering component: ConvertScriptDataComponent -> ConvertScriptDataComponent
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent']
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:63] Registering component: DataToDataFrameComponent -> DataToDataFrameComponent
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent']
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:63] Registering component: MessageToDataComponent -> MessageToDataExecutor
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent']
2025-06-02 19:18:56 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.alter_metadata_component
2025-06-02 19:18:56 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.api_component
2025-06-02 19:18:56 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.api_component
2025-06-02 19:18:56 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.combine_text_component
2025-06-02 19:18:56 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.combine_text_component
2025-06-02 19:18:56 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.combine_text_component_new
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:63] Registering component: CombineTextComponent -> CombineTextComponent
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent']
2025-06-02 19:18:56 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.combine_text_component_new
2025-06-02 19:18:56 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.convert_script_data_component
2025-06-02 19:18:56 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.convert_script_data_component
2025-06-02 19:18:56 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.data_to_dataframe_component
2025-06-02 19:18:56 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.data_to_dataframe_component
2025-06-02 19:18:56 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.doc_component
2025-06-02 19:18:56 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.doc_component
2025-06-02 19:18:56 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.dynamic_combine_text_component
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:63] Registering component: CombineTextExecutor -> CombineTextExecutor
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor']
2025-06-02 19:18:56 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.dynamic_combine_text_component
2025-06-02 19:18:56 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.gmail_component
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:63] Registering component: GmailComponent -> GmailComponent
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor', 'GmailComponent']
2025-06-02 19:18:56 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.gmail_component
2025-06-02 19:18:56 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.gmail_tracker_component
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:63] Registering component: GmailTrackerComponent -> GmailTrackerComponent
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor', 'GmailComponent', 'GmailTrackerComponent']
2025-06-02 19:18:56 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.gmail_tracker_component
2025-06-02 19:18:56 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.id_generator_component
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:63] Registering component: IDGeneratorComponent -> IDGeneratorComponent
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor', 'GmailComponent', 'GmailTrackerComponent', 'IDGeneratorComponent']
2025-06-02 19:18:56 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.id_generator_component
2025-06-02 19:18:56 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.merge_data_component
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:63] Registering component: MergeDataComponent -> MergeDataExecutor
2025-06-02 19:18:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor', 'GmailComponent', 'GmailTrackerComponent', 'IDGeneratorComponent', 'MergeDataComponent']
2025-06-02 19:18:56 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.merge_data_component
2025-06-02 19:18:56 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.message_to_data_component
2025-06-02 19:18:56 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.message_to_data_component
2025-06-02 19:18:56 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.select_data_component
2025-06-02 19:18:56 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.select_data_component
2025-06-02 19:18:56 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.split_text_component
2025-06-02 19:18:56 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.split_text_component
2025-06-02 19:18:56 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.text_analysis_component
2025-06-02 19:18:56 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.text_analysis_component
2025-06-02 19:18:56 - ComponentSystem - INFO - [discover_component_modules:1371] Imported 16 component modules: ['app.components.alter_metadata_component', 'app.components.api_component', 'app.components.combine_text_component', 'app.components.combine_text_component_new', 'app.components.convert_script_data_component', 'app.components.data_to_dataframe_component', 'app.components.doc_component', 'app.components.dynamic_combine_text_component', 'app.components.gmail_component', 'app.components.gmail_tracker_component', 'app.components.id_generator_component', 'app.components.merge_data_component', 'app.components.message_to_data_component', 'app.components.select_data_component', 'app.components.split_text_component', 'app.components.text_analysis_component']
2025-06-02 19:18:56 - ComponentSystem - INFO - [start_component:330] Creating Kafka consumer for component ApiRequestNode with configuration:
2025-06-02 19:18:56 - ComponentSystem - INFO - [start_component:333]   Bootstrap Servers: **************:9092
2025-06-02 19:18:56 - ComponentSystem - INFO - [start_component:334]   Group ID: node_executor_service
2025-06-02 19:18:56 - ComponentSystem - INFO - [start_component:335]   Topic: node-execution-request
2025-06-02 19:18:56 - ComponentSystem - INFO - [start_component:336]   Auto Offset Reset: latest (starting from the latest offset)
2025-06-02 19:18:56 - ComponentSystem - INFO - [start_component:337]   Auto Commit: Disabled (using manual offset commits)
2025-06-02 19:18:56 - ComponentSystem - INFO - [start_component:338]   Fetch Min Bytes: 100
2025-06-02 19:18:56 - ComponentSystem - INFO - [start_component:339]   Fetch Max Wait: 500ms
2025-06-02 19:18:56 - ComponentSystem - INFO - [start_component:340]   Session Timeout: 10000ms
2025-06-02 19:18:56 - ComponentSystem - INFO - [start_component:343]   Heartbeat Interval: 3000ms
2025-06-02 19:18:56 - ComponentSystem - INFO - [start_component:347] Creating new Kafka consumer for component: ApiRequestNode on topic: node-execution-request with group_id: node_executor_service
2025-06-02 19:19:02 - ComponentSystem - INFO - [start_component:356] Kafka consumer started successfully for component: ApiRequestNode
2025-06-02 19:19:02 - ComponentSystem - INFO - [start_component:378] Started component: ApiRequestNode, listening on topic: node-execution-request
2025-06-02 19:19:02 - ComponentSystem - INFO - [_consume_messages:501] Consumer loop started for component: ApiRequestNode
2025-06-02 19:19:03 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=446, TaskID=ApiRequestNode-node-execution-request-0-446-1748872143.0582855
2025-06-02 19:19:03 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-446-1748872143.0582855, Payload={
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://ruh-admin-api.rapidinnovation.dev/api/v1/auth/service/login",
    "method": "POST",
    "query_params": null,
    "headers": {
      "Content-Type": "application/json"
    },
    "body": {
      "title": "Optimizing Business Processes: The Role of AI Agent Workflows with Ruh.ai",
      "summary": "Delve into the transformative impact of AI agent workflows in enhancing business efficiency. This blog post explores the intricacies of implementing AI-driven tasks, outlines the benefits for enterprise operations, and highlights how Ruh.ai innovates these processes through cutting-edge solutions.",
      "category": "AI Innovation",
      "content": "<h1>Understanding AI Agent Workflows</h1><p>AI agent workflows are pivotal in modernizing how businesses operate, allowing for the automation of complex task sequences through intelligent systems. These workflows integrate AI capabilities to enhance decision-making, minimize human error, and streamline processes across various sectors.</p><h2>The Evolution of AI Workforce</h2><p>Historically, business tasks relied heavily on human workforce for execution. With AI, businesses can deploy digital agents that not only automate tedious tasks but also adapt to new information, learning and evolving with every interaction. This evolution marks a significant leap from manual operations to dynamic AI-driven solutions.</p><h3>Key Components of Effective AI Workflows</h3><ul><li><strong>Automation:</strong> Automates repetitive tasks, freeing human resources for strategic initiatives.</li><li><strong>Adaptation:</strong> AI agents can learn from data, adapt to changes, and continuously improve accuracy and efficiency.</li><li><strong>Integration:</strong> Seamlessly integrates with existing systems, ensuring minimal disruption and maximizing resource utility.</li></ul><h2>Strategic Benefits of AI Workflows</h2><p>AI workflows provide substantial benefits to enterprises by driving efficiency and accuracy. With AI agents handling routine processes, businesses can focus on innovation, customer engagement, and strategic growth. Ruh.ai specifically leverages AI workflows to offer unparalleled solutions tailored to client needs.</p><h3>Enhanced Decision-Making</h3><p>AI agent workflows enable superior decision-making capabilities through advanced data analysis and insights. This empowers businesses to make informed decisions with confidence and precision.</p><h3>Operational Agility</h3><p>By implementing AI workflows, businesses gain the agility needed to respond swiftly to market changes and consumer demands, staying ahead of the competition.</p><h1>Implementing AI Workflows with Ruh.ai</h1><p>Embracing AI workflow integration demands a thorough understanding of business needs and technological readiness. Ruh.ai excels in crafting bespoke AI solutions that cater to various industries, ensuring smooth transitions and maximizing the potential of AI integration.</p><h2>Analyzing Business Needs</h2><p>Before implementing AI workflows, assessing existing processes and identifying areas for improvement is crucial. This helps in defining clear objectives and aligning AI strategies with business goals.</p><h2>Defining Clean Workflow States</h2><p>Ensuring that workflows are clearly defined, with clean inputs and outputs, guarantees seamless AI integration. Ruh.ai provides expert guidance in developing efficient workflows that are robust and scalable, ready to meet future challenges head-on.</p>"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "44a55d0b-3670-4f42-bd5c-76259ae4e902",
  "correlation_id": "daad5b2d-878f-46dd-866f-a0bc4492f270"
}
2025-06-02 19:19:03 - ComponentSystem - INFO - [_process_message:713] [ReqID:44a55d0b-3670-4f42-bd5c-76259ae4e902] [CorrID:daad5b2d-878f-46dd-866f-a0bc4492f270] Executing tool ApiRequestNode for RequestID=44a55d0b-3670-4f42-bd5c-76259ae4e902, TaskID=ApiRequestNode-node-execution-request-0-446-1748872143.0582855
2025-06-02 19:19:04 - ComponentSystem - INFO - [_process_message:717] [ReqID:44a55d0b-3670-4f42-bd5c-76259ae4e902] [CorrID:daad5b2d-878f-46dd-866f-a0bc4492f270] Tool ApiRequestNode executed successfully for RequestID=44a55d0b-3670-4f42-bd5c-76259ae4e902, TaskID=ApiRequestNode-node-execution-request-0-446-1748872143.0582855
2025-06-02 19:19:04 - ComponentSystem - INFO - [_send_result:1005] [ReqID:44a55d0b-3670-4f42-bd5c-76259ae4e902] [CorrID:daad5b2d-878f-46dd-866f-a0bc4492f270] Preparing to send result for component ApiRequestNode, RequestID=44a55d0b-3670-4f42-bd5c-76259ae4e902
2025-06-02 19:19:04 - ComponentSystem - INFO - [get_producer:244] [ReqID:44a55d0b-3670-4f42-bd5c-76259ae4e902] [CorrID:daad5b2d-878f-46dd-866f-a0bc4492f270] Creating Kafka producer for component ApiRequestNode with configuration:
2025-06-02 19:19:04 - ComponentSystem - INFO - [get_producer:247] [ReqID:44a55d0b-3670-4f42-bd5c-76259ae4e902] [CorrID:daad5b2d-878f-46dd-866f-a0bc4492f270]   Bootstrap Servers: **************:9092
2025-06-02 19:19:04 - ComponentSystem - INFO - [get_producer:248] [ReqID:44a55d0b-3670-4f42-bd5c-76259ae4e902] [CorrID:daad5b2d-878f-46dd-866f-a0bc4492f270]   Acks: all (ensuring message is written to all in-sync replicas)
2025-06-02 19:19:04 - ComponentSystem - INFO - [get_producer:252] [ReqID:44a55d0b-3670-4f42-bd5c-76259ae4e902] [CorrID:daad5b2d-878f-46dd-866f-a0bc4492f270]   Request Timeout: 60000ms
2025-06-02 19:19:04 - ComponentSystem - INFO - [get_producer:255] [ReqID:44a55d0b-3670-4f42-bd5c-76259ae4e902] [CorrID:daad5b2d-878f-46dd-866f-a0bc4492f270]   Idempotence: Enabled (ensuring exactly-once delivery semantics)
2025-06-02 19:19:04 - ComponentSystem - INFO - [get_producer:259] [ReqID:44a55d0b-3670-4f42-bd5c-76259ae4e902] [CorrID:daad5b2d-878f-46dd-866f-a0bc4492f270] Creating new Kafka producer for component: ApiRequestNode with servers: **************:9092
2025-06-02 19:19:06 - ComponentSystem - INFO - [get_producer:266] [ReqID:44a55d0b-3670-4f42-bd5c-76259ae4e902] [CorrID:daad5b2d-878f-46dd-866f-a0bc4492f270] Kafka producer started successfully for component: ApiRequestNode
2025-06-02 19:19:06 - ComponentSystem - INFO - [_send_result:1037] [ReqID:44a55d0b-3670-4f42-bd5c-76259ae4e902] [CorrID:daad5b2d-878f-46dd-866f-a0bc4492f270] Component returned error status for RequestID=44a55d0b-3670-4f42-bd5c-76259ae4e902: API request failed with status 422 (Unprocessable Entity)
2025-06-02 19:19:06 - ComponentSystem - INFO - [_send_result:1084] [ReqID:44a55d0b-3670-4f42-bd5c-76259ae4e902] [CorrID:daad5b2d-878f-46dd-866f-a0bc4492f270] Sending Kafka response: RequestID=44a55d0b-3670-4f42-bd5c-76259ae4e902, Response={
  "request_id": "44a55d0b-3670-4f42-bd5c-76259ae4e902",
  "component_type": "ApiRequestNode",
  "status": "error",
  "timestamp": 1748872146.0163383,
  "result": null,
  "error": "API request failed with status 422 (Unprocessable Entity)"
}
2025-06-02 19:19:06 - ComponentSystem - INFO - [_send_result:1093] [ReqID:44a55d0b-3670-4f42-bd5c-76259ae4e902] [CorrID:daad5b2d-878f-46dd-866f-a0bc4492f270] Sent result for component ApiRequestNode to topic node_results for RequestID=44a55d0b-3670-4f42-bd5c-76259ae4e902
2025-06-02 19:19:06 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:44a55d0b-3670-4f42-bd5c-76259ae4e902] [CorrID:daad5b2d-878f-46dd-866f-a0bc4492f270] Successfully committed offset 447 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-446-1748872143.0582855
2025-06-02 19:19:06 - ComponentSystem - INFO - [_process_message:936] [ReqID:44a55d0b-3670-4f42-bd5c-76259ae4e902] [CorrID:daad5b2d-878f-46dd-866f-a0bc4492f270] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=446, TaskID=ApiRequestNode-node-execution-request-0-446-1748872143.0582855
2025-06-02 19:34:09 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=447, TaskID=ApiRequestNode-node-execution-request-0-447-1748873049.3480694
2025-06-02 19:34:09 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-447-1748873049.3480694, Payload={
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://ruh-admin-api.rapidinnovation.dev/api/v1/auth/service/login",
    "method": "POST",
    "query_params": null,
    "headers": {
      "Content-Type": "application/json"
    },
    "body": {
      "title": "Optimizing Business Processes: The Role of AI Agent Workflows with Ruh.ai",
      "summary": "Delve into the transformative impact of AI agent workflows in enhancing business efficiency. This blog post explores the intricacies of implementing AI-driven tasks, outlines the benefits for enterprise operations, and highlights how Ruh.ai innovates these processes through cutting-edge solutions.",
      "category": "AI Innovation",
      "content": "<h1>Understanding AI Agent Workflows</h1><p>AI agent workflows are pivotal in modernizing how businesses operate, allowing for the automation of complex task sequences through intelligent systems. These workflows integrate AI capabilities to enhance decision-making, minimize human error, and streamline processes across various sectors.</p><h2>The Evolution of AI Workforce</h2><p>Historically, business tasks relied heavily on human workforce for execution. With AI, businesses can deploy digital agents that not only automate tedious tasks but also adapt to new information, learning and evolving with every interaction. This evolution marks a significant leap from manual operations to dynamic AI-driven solutions.</p><h3>Key Components of Effective AI Workflows</h3><ul><li><strong>Automation:</strong> Automates repetitive tasks, freeing human resources for strategic initiatives.</li><li><strong>Adaptation:</strong> AI agents can learn from data, adapt to changes, and continuously improve accuracy and efficiency.</li><li><strong>Integration:</strong> Seamlessly integrates with existing systems, ensuring minimal disruption and maximizing resource utility.</li></ul><h2>Strategic Benefits of AI Workflows</h2><p>AI workflows provide substantial benefits to enterprises by driving efficiency and accuracy. With AI agents handling routine processes, businesses can focus on innovation, customer engagement, and strategic growth. Ruh.ai specifically leverages AI workflows to offer unparalleled solutions tailored to client needs.</p><h3>Enhanced Decision-Making</h3><p>AI agent workflows enable superior decision-making capabilities through advanced data analysis and insights. This empowers businesses to make informed decisions with confidence and precision.</p><h3>Operational Agility</h3><p>By implementing AI workflows, businesses gain the agility needed to respond swiftly to market changes and consumer demands, staying ahead of the competition.</p><h1>Implementing AI Workflows with Ruh.ai</h1><p>Embracing AI workflow integration demands a thorough understanding of business needs and technological readiness. Ruh.ai excels in crafting bespoke AI solutions that cater to various industries, ensuring smooth transitions and maximizing the potential of AI integration.</p><h2>Analyzing Business Needs</h2><p>Before implementing AI workflows, assessing existing processes and identifying areas for improvement is crucial. This helps in defining clear objectives and aligning AI strategies with business goals.</p><h2>Defining Clean Workflow States</h2><p>Ensuring that workflows are clearly defined, with clean inputs and outputs, guarantees seamless AI integration. Ruh.ai provides expert guidance in developing efficient workflows that are robust and scalable, ready to meet future challenges head-on.</p>"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "8effc549-cdd8-482e-b5a1-869dcdf882a0",
  "correlation_id": "33389210-241f-40f1-a54c-02d41f4ad526"
}
2025-06-02 19:34:09 - ComponentSystem - INFO - [_process_message:713] [ReqID:8effc549-cdd8-482e-b5a1-869dcdf882a0] [CorrID:33389210-241f-40f1-a54c-02d41f4ad526] Executing tool ApiRequestNode for RequestID=8effc549-cdd8-482e-b5a1-869dcdf882a0, TaskID=ApiRequestNode-node-execution-request-0-447-1748873049.3480694
2025-06-02 19:34:10 - ComponentSystem - INFO - [_process_message:717] [ReqID:8effc549-cdd8-482e-b5a1-869dcdf882a0] [CorrID:33389210-241f-40f1-a54c-02d41f4ad526] Tool ApiRequestNode executed successfully for RequestID=8effc549-cdd8-482e-b5a1-869dcdf882a0, TaskID=ApiRequestNode-node-execution-request-0-447-1748873049.3480694
2025-06-02 19:34:10 - ComponentSystem - INFO - [_send_result:1005] [ReqID:8effc549-cdd8-482e-b5a1-869dcdf882a0] [CorrID:33389210-241f-40f1-a54c-02d41f4ad526] Preparing to send result for component ApiRequestNode, RequestID=8effc549-cdd8-482e-b5a1-869dcdf882a0
2025-06-02 19:34:10 - ComponentSystem - INFO - [_send_result:1037] [ReqID:8effc549-cdd8-482e-b5a1-869dcdf882a0] [CorrID:33389210-241f-40f1-a54c-02d41f4ad526] Component returned error status for RequestID=8effc549-cdd8-482e-b5a1-869dcdf882a0: API request failed with status 422 (Unprocessable Entity)
2025-06-02 19:34:10 - ComponentSystem - INFO - [_send_result:1084] [ReqID:8effc549-cdd8-482e-b5a1-869dcdf882a0] [CorrID:33389210-241f-40f1-a54c-02d41f4ad526] Sending Kafka response: RequestID=8effc549-cdd8-482e-b5a1-869dcdf882a0, Response={
  "request_id": "8effc549-cdd8-482e-b5a1-869dcdf882a0",
  "component_type": "ApiRequestNode",
  "status": "error",
  "timestamp": 1748873050.1621227,
  "result": null,
  "error": "API request failed with status 422 (Unprocessable Entity)"
}
2025-06-02 19:34:10 - ComponentSystem - INFO - [_send_result:1093] [ReqID:8effc549-cdd8-482e-b5a1-869dcdf882a0] [CorrID:33389210-241f-40f1-a54c-02d41f4ad526] Sent result for component ApiRequestNode to topic node_results for RequestID=8effc549-cdd8-482e-b5a1-869dcdf882a0
2025-06-02 19:34:10 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:8effc549-cdd8-482e-b5a1-869dcdf882a0] [CorrID:33389210-241f-40f1-a54c-02d41f4ad526] Successfully committed offset 448 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-447-1748873049.3480694
2025-06-02 19:34:10 - ComponentSystem - INFO - [_process_message:936] [ReqID:8effc549-cdd8-482e-b5a1-869dcdf882a0] [CorrID:33389210-241f-40f1-a54c-02d41f4ad526] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=447, TaskID=ApiRequestNode-node-execution-request-0-447-1748873049.3480694
2025-06-02 19:35:25 - ComponentSystem - INFO - [stop_all_components:481] Stopping all running components...
2025-06-02 19:35:25 - ComponentSystem - INFO - [stop_component:413] Stopping component: ApiRequestNode
2025-06-02 19:35:25 - ComponentSystem - INFO - [_consume_messages:601] Consumer task for component ApiRequestNode cancelled
2025-06-02 19:35:25 - ComponentSystem - INFO - [_consume_messages:608] Consumer loop finished for component: ApiRequestNode
