2025-06-02 19:50:55 - ComponentSystem - INFO - [setup_logger:467] Logger ComponentSystem configured with log file: logs\2025-06-02\ComponentSystem_19-50-55.log
2025-06-02 19:50:55 - ComponentSystem - INFO - [decorator:63] Registering component: ApiRequestNode -> ApiComponent
2025-06-02 19:50:55 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode']
2025-06-02 19:50:55 - ComponentSystem - INFO - [decorator:63] Registering component: CombineTextComponent -> CombineTextComponent
2025-06-02 19:50:55 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent']
2025-06-02 19:50:55 - ComponentSystem - INFO - [decorator:63] Registering component: combine_text -> CombineTextComponent
2025-06-02 19:50:55 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text']
2025-06-02 19:50:55 - ComponentSystem - INFO - [decorator:63] Registering component: DocComponent -> DocComponent
2025-06-02 19:50:55 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent']
2025-06-02 19:50:55 - ComponentSystem - INFO - [decorator:63] Registering component: SelectDataComponent -> SelectDataExecutor
2025-06-02 19:50:55 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent']
2025-06-02 19:50:55 - ComponentSystem - INFO - [decorator:63] Registering component: SplitTextComponent -> SplitTextComponent
2025-06-02 19:50:55 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent']
2025-06-02 19:50:55 - ComponentSystem - INFO - [decorator:63] Registering component: text_analysis -> TextAnalysisComponent
2025-06-02 19:50:55 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis']
2025-06-02 19:50:55 - ComponentSystem - INFO - [decorator:63] Registering component: AlterMetadataComponent -> AlterMetadataComponent
2025-06-02 19:50:55 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent']
2025-06-02 19:50:55 - ComponentSystem - INFO - [decorator:63] Registering component: ConvertScriptDataComponent -> ConvertScriptDataComponent
2025-06-02 19:50:55 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent']
2025-06-02 19:50:55 - ComponentSystem - INFO - [decorator:63] Registering component: DataToDataFrameComponent -> DataToDataFrameComponent
2025-06-02 19:50:55 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent']
2025-06-02 19:50:55 - ComponentSystem - INFO - [decorator:63] Registering component: MessageToDataComponent -> MessageToDataExecutor
2025-06-02 19:50:55 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent']
