2025-06-02 19:53:08 - ComponentSystem - INFO - [setup_logger:467] Logger ComponentSystem configured with log file: logs\2025-06-02\ComponentSystem_19-53-08.log
2025-06-02 19:53:08 - ComponentSystem - INFO - [get_component_manager:1393] Creating new global ComponentManager instance
2025-06-02 19:53:08 - ComponentSystem - INFO - [__init__:87] Initializing ComponentManager with Kafka bootstrap servers: **************:9092
2025-06-02 19:53:08 - ComponentSystem - INFO - [__init__:92] Kafka Consumer Topic: node-execution-request
2025-06-02 19:53:08 - ComponentSystem - INFO - [__init__:93] Kafka Results Topic: node_results
2025-06-02 19:53:08 - ComponentSystem - INFO - [__init__:94] Kafka Consumer Group ID: node_executor_service
2025-06-02 19:53:08 - ComponentSystem - INFO - [__init__:95] Kafka Producer Request Timeout: 60000ms
2025-06-02 19:53:08 - ComponentSystem - INFO - [__init__:98] Kafka Consumer Fetch Min Bytes: 100
2025-06-02 19:53:08 - ComponentSystem - INFO - [__init__:101] Kafka Consumer Fetch Max Wait: 500ms
2025-06-02 19:53:08 - ComponentSystem - INFO - [__init__:104] Kafka Consumer Session Timeout: 10000ms
2025-06-02 19:53:08 - ComponentSystem - INFO - [__init__:107] Kafka Consumer Heartbeat Interval: 3000ms
2025-06-02 19:53:08 - ComponentSystem - INFO - [__init__:110] Default Node Retries: 3
2025-06-02 19:53:08 - ComponentSystem - INFO - [__init__:128] Initializing ThreadPoolExecutor with 4 workers
2025-06-02 19:53:08 - ComponentSystem - INFO - [__init__:132] ThreadPoolExecutor initialized
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_components:141] Discovering components
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_components:142] Component registry before discovery: []
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_components:153] Discovered components: []
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1332] Discovering component modules
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1348] Found 16 potential component files: ['alter_metadata_component.py', 'api_component.py', 'combine_text_component.py', 'combine_text_component_new.py', 'convert_script_data_component.py', 'data_to_dataframe_component.py', 'doc_component.py', 'dynamic_combine_text_component.py', 'gmail_component.py', 'gmail_tracker_component.py', 'id_generator_component.py', 'merge_data_component.py', 'message_to_data_component.py', 'select_data_component.py', 'split_text_component.py', 'text_analysis_component.py']
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.alter_metadata_component
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:63] Registering component: ApiRequestNode -> ApiComponent
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode']
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:63] Registering component: CombineTextComponent -> CombineTextComponent
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent']
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:63] Registering component: combine_text -> CombineTextComponent
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text']
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:63] Registering component: DocComponent -> DocComponent
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent']
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:63] Registering component: SelectDataComponent -> SelectDataExecutor
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent']
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:63] Registering component: SplitTextComponent -> SplitTextComponent
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent']
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:63] Registering component: text_analysis -> TextAnalysisComponent
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis']
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:63] Registering component: AlterMetadataComponent -> AlterMetadataComponent
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent']
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:63] Registering component: ConvertScriptDataComponent -> ConvertScriptDataComponent
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent']
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:63] Registering component: DataToDataFrameComponent -> DataToDataFrameComponent
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent']
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:63] Registering component: MessageToDataComponent -> MessageToDataExecutor
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent']
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.alter_metadata_component
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.api_component
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.api_component
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.combine_text_component
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.combine_text_component
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.combine_text_component_new
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:63] Registering component: CombineTextComponent -> CombineTextComponent
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent']
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.combine_text_component_new
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.convert_script_data_component
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.convert_script_data_component
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.data_to_dataframe_component
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.data_to_dataframe_component
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.doc_component
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.doc_component
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.dynamic_combine_text_component
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:63] Registering component: CombineTextExecutor -> CombineTextExecutor
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor']
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.dynamic_combine_text_component
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.gmail_component
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:63] Registering component: GmailComponent -> GmailComponent
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor', 'GmailComponent']
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.gmail_component
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.gmail_tracker_component
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:63] Registering component: GmailTrackerComponent -> GmailTrackerComponent
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor', 'GmailComponent', 'GmailTrackerComponent']
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.gmail_tracker_component
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.id_generator_component
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:63] Registering component: IDGeneratorComponent -> IDGeneratorComponent
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor', 'GmailComponent', 'GmailTrackerComponent', 'IDGeneratorComponent']
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.id_generator_component
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.merge_data_component
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:63] Registering component: MergeDataComponent -> MergeDataExecutor
2025-06-02 19:53:08 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor', 'GmailComponent', 'GmailTrackerComponent', 'IDGeneratorComponent', 'MergeDataComponent']
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.merge_data_component
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.message_to_data_component
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.message_to_data_component
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.select_data_component
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.select_data_component
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.split_text_component
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.split_text_component
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1358] Importing component module: app.components.text_analysis_component
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1362] Successfully imported component module: app.components.text_analysis_component
2025-06-02 19:53:08 - ComponentSystem - INFO - [discover_component_modules:1371] Imported 16 component modules: ['app.components.alter_metadata_component', 'app.components.api_component', 'app.components.combine_text_component', 'app.components.combine_text_component_new', 'app.components.convert_script_data_component', 'app.components.data_to_dataframe_component', 'app.components.doc_component', 'app.components.dynamic_combine_text_component', 'app.components.gmail_component', 'app.components.gmail_tracker_component', 'app.components.id_generator_component', 'app.components.merge_data_component', 'app.components.message_to_data_component', 'app.components.select_data_component', 'app.components.split_text_component', 'app.components.text_analysis_component']
2025-06-02 19:53:08 - ComponentSystem - INFO - [start_component:330] Creating Kafka consumer for component ApiRequestNode with configuration:
2025-06-02 19:53:08 - ComponentSystem - INFO - [start_component:333]   Bootstrap Servers: **************:9092
2025-06-02 19:53:08 - ComponentSystem - INFO - [start_component:334]   Group ID: node_executor_service
2025-06-02 19:53:08 - ComponentSystem - INFO - [start_component:335]   Topic: node-execution-request
2025-06-02 19:53:08 - ComponentSystem - INFO - [start_component:336]   Auto Offset Reset: latest (starting from the latest offset)
2025-06-02 19:53:08 - ComponentSystem - INFO - [start_component:337]   Auto Commit: Disabled (using manual offset commits)
2025-06-02 19:53:08 - ComponentSystem - INFO - [start_component:338]   Fetch Min Bytes: 100
2025-06-02 19:53:08 - ComponentSystem - INFO - [start_component:339]   Fetch Max Wait: 500ms
2025-06-02 19:53:08 - ComponentSystem - INFO - [start_component:340]   Session Timeout: 10000ms
2025-06-02 19:53:08 - ComponentSystem - INFO - [start_component:343]   Heartbeat Interval: 3000ms
2025-06-02 19:53:08 - ComponentSystem - INFO - [start_component:347] Creating new Kafka consumer for component: ApiRequestNode on topic: node-execution-request with group_id: node_executor_service
