2025-06-02 19:50:55 - SelectDataComponent - INFO - [setup_logger:467] Logger SelectDataComponent configured with log file: logs\2025-06-02\SelectDataComponent_19-50-55.log
2025-06-02 19:50:55 - SelectDataComponent - INFO - [<module>:546] SelectDataComponent successfully registered in COMPONENT_REGISTRY
2025-06-02 19:50:55 - SelectDataComponent - INFO - [__init__:43] SelectDataExecutor initialized
2025-06-02 19:50:55 - SelectDataComponent - INFO - [process:348] Processing select data request for request_id: test-request-123
2025-06-02 19:50:55 - SelectDataComponent - INFO - [process:353] Found 'tool_parameters' field in payload. Using it for parameters.
2025-06-02 19:50:55 - SelectDataComponent - INFO - [process:362] PARAMETERS KEYS: ['input_data', 'data_type', 'search_mode', 'field_matching_mode', 'selector', 'request_id']
2025-06-02 19:50:55 - SelectDataComponent - INFO - [process:372] [DEBUG] Input data: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIyOWUyZjVkYy1mZjM0LTQ5NWQtYjFmZS00YTQ0ZWZjMzAxNWIiLCJ0eXBlIjoic2VydmljZSIsInNlcnZpY2VfbmFtZSI6ImJsb2ctZ2VuZXJhdG9yIiwiZXhwIjoxNzQ4OTYwMDI3fQ.QUOYpLfIrXmB30AZTIPppXUYcUaG3SM8VDcUCpAq-aA', 'token_type': 'bearer'}
2025-06-02 19:50:55 - SelectDataComponent - INFO - [process:373] [DEBUG] Selector: access_token
2025-06-02 19:50:55 - SelectDataComponent - INFO - [process:374] [DEBUG] Search mode: Smart Search
2025-06-02 19:50:55 - SelectDataComponent - INFO - [process:375] [DEBUG] Field matching mode: None
2025-06-02 19:50:55 - SelectDataComponent - INFO - [_smart_search_field:219] [DEBUG] Smart searching for field 'access_token' in dict with mode 'None'
2025-06-02 19:50:55 - SelectDataComponent - INFO - [_smart_search_field:222] [DEBUG] Data is dict with keys: ['access_token', 'token_type']
2025-06-02 19:50:55 - SelectDataComponent - INFO - [_smart_search_field:219] [DEBUG] Smart searching for field 'access_token' in str with mode 'None'
2025-06-02 19:50:55 - SelectDataComponent - INFO - [_smart_search_field:219] [DEBUG] Smart searching for field 'access_token' in str with mode 'None'
2025-06-02 19:50:55 - SelectDataComponent - INFO - [process:440] Data selected successfully for request_id test-request-123. Result type: NoneType
2025-06-02 19:50:55 - SelectDataComponent - INFO - [process:348] Processing select data request for request_id: test-request-123
2025-06-02 19:50:55 - SelectDataComponent - INFO - [process:353] Found 'tool_parameters' field in payload. Using it for parameters.
2025-06-02 19:50:55 - SelectDataComponent - INFO - [process:362] PARAMETERS KEYS: ['input_data', 'data_type', 'search_mode', 'field_matching_mode', 'selector', 'request_id']
2025-06-02 19:50:55 - SelectDataComponent - INFO - [process:372] [DEBUG] Input data: {'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIyOWUyZjVkYy1mZjM0LTQ5NWQtYjFmZS00YTQ0ZWZjMzAxNWIiLCJ0eXBlIjoic2VydmljZSIsInNlcnZpY2VfbmFtZSI6ImJsb2ctZ2VuZXJhdG9yIiwiZXhwIjoxNzQ4OTYwMDI3fQ.QUOYpLfIrXmB30AZTIPppXUYcUaG3SM8VDcUCpAq-aA', 'token_type': 'bearer'}
2025-06-02 19:50:55 - SelectDataComponent - INFO - [process:373] [DEBUG] Selector: access_token
2025-06-02 19:50:55 - SelectDataComponent - INFO - [process:374] [DEBUG] Search mode: Exact Path
2025-06-02 19:50:55 - SelectDataComponent - INFO - [process:375] [DEBUG] Field matching mode: None
2025-06-02 19:50:55 - SelectDataComponent - INFO - [process:440] Data selected successfully for request_id test-request-123. Result type: str
