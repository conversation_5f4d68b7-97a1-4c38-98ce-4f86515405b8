2025-06-02 19:52:28 - SelectDataComponent - INFO - [setup_logger:467] Logger SelectDataComponent configured with log file: logs\2025-06-02\SelectDataComponent_19-52-28.log
2025-06-02 19:52:28 - SelectDataComponent - INFO - [<module>:536] SelectDataComponent successfully registered in COMPONENT_REGISTRY
2025-06-02 19:52:28 - SelectDataComponent - INFO - [__init__:43] SelectDataExecutor initialized
2025-06-02 19:52:28 - SelectDataComponent - INFO - [process:345] Processing select data request for request_id: test-request-123
2025-06-02 19:52:28 - SelectDataComponent - INFO - [process:350] Found 'tool_parameters' field in payload. Using it for parameters.
2025-06-02 19:52:28 - SelectDataComponent - INFO - [process:359] PARAMETERS KEYS: ['input_data', 'data_type', 'search_mode', 'field_matching_mode', 'selector', 'request_id']
2025-06-02 19:52:28 - SelectDataComponent - INFO - [process:430] Data selected successfully for request_id test-request-123. Result type: str
2025-06-02 19:52:28 - SelectDataComponent - INFO - [process:345] Processing select data request for request_id: test-request-123
2025-06-02 19:52:28 - SelectDataComponent - INFO - [process:350] Found 'tool_parameters' field in payload. Using it for parameters.
2025-06-02 19:52:28 - SelectDataComponent - INFO - [process:359] PARAMETERS KEYS: ['input_data', 'data_type', 'search_mode', 'field_matching_mode', 'selector', 'request_id']
2025-06-02 19:52:28 - SelectDataComponent - INFO - [process:430] Data selected successfully for request_id test-request-123. Result type: str
