# app/config/config.py (adjust path if needed)

from pathlib import Path
from typing import Optional
from pydantic import Field, SecretStr, computed_field  # type: ignore
from pydantic_settings import BaseSettings, SettingsConfigDict  # type: ignore


class Settings(BaseSettings):
    """
    Loads and validates application settings from environment variables
    and a .env file based on the provided .env.example structure.
    Includes settings required by KafkaToolExecutor.
    """

    # --- Application settings ---
    app_name: str = Field(default="Orchestration Engine", alias="APP_NAME")
    app_env: str = Field(default="Production", alias="APP_ENV")
    app_debug: bool = Field(default=False, alias="APP_DEBUG")

    # --- OpenAI API Keys ---
    openai_api_key: Optional[SecretStr] = Field(..., alias="OPENAI_API_KEY")
    model_name: Optional[str] = Field(..., alias="MODEL_NAME")

    # --- Kafka Configuration ---
    kafka_bootstrap_servers: str = Field(..., alias="KAFKA_BOOTSTRAP_SERVERS")

    # Topic where Tool Execution requests are SENT (by KafkaToolExecutor)
    kafka_mcp_execution_request_topic: str = Field(
        default="mcp-execution-request", alias="KAFKA_MCP_EXECUTION_REQUEST_TOPIC"
    )
    kafka_mcp_execution_result_topic: str = Field(
        default="mcp_results", alias="KAFKA_MCP_EXECUTION_RESULT_TOPIC"
    )

    # Topic where Node Execution requests are SENT (by NodeExecutor)
    kafka_node_execution_request_topic: str = Field(
        default="node-execution-request", alias="KAFKA_NODE_EXECUTION_REQUEST_TOPIC"
    )
    kafka_node_execution_result_topic: str = Field(
        default="node_results", alias="KAFKA_NODE_EXECUTION_RESULT_TOPIC"
    )

    # Topic where Agent Execution requests are SENT (by AgentExecutor)
    kafka_agent_execution_request_topic: str = Field(
        default="agent-execution-request", alias="KAFKA_AGENT_EXECUTION_REQUEST_TOPIC"
    )
    kafka_agent_execution_result_topic: str = Field(
        default="agent_results", alias="KAFKA_AGENT_EXECUTION_RESULT_TOPIC"
    )

    # Consumer group ID for the MAIN service (not the KafkaToolExecutor's internal one)
    kafka_main_consumer_group_id: str = Field(
        default="orchestration-engine", alias="GROUP_ID"
    )

    # Topic where Workflow Execution requests are RECEIVED (by the main execution service)
    kafka_workflow_request_topic: Optional[str] = Field(
        default="workflow-requests", alias="KAFKA_WORKFLOW_REQUEST_TOPIC"
    )
    kafka_execution_request_topic: Optional[str] = Field(
        default="execution-requests", alias="KAFKA_EXECUTION_REQUEST_TOPIC"
    )
    # Define KAFKA_APPROVAL_REQUEST_TOPIC in .env if used
    kafka_approval_request_topic: Optional[str] = Field(
        default="approval-requests", alias="KAFKA_APPROVAL_REQUEST_TOPIC"
    )

    # --- Redis settings ---
    redis_host: str = Field(..., alias="REDIS_HOST")
    redis_port: int = Field(..., alias="REDIS_PORT")
    redis_results_db_index: int = Field(default=0, alias="REDIS_RESULTS_DB_INDEX")
    redis_state_db_index: int = Field(default=1, alias="REDIS_STATE_DB_INDEX")
    redis_password: Optional[SecretStr] = Field(..., alias="REDIS_PASSWORD")
    redis_results_ttl: int = Field(
        default=300, alias="REDIS_RESULTS_TTL"
    )  # 5 minutes in seconds
    redis_state_ttl: int = Field(
        default=600, alias="REDIS_STATE_TTL"
    )  # 10 minutes in seconds

    # --- PostgreSQL settings ---
    db_host: str = Field(..., alias="DB_HOST")
    db_port: int = Field(..., alias="DB_PORT")
    db_user: str = Field(..., alias="DB_USER")
    db_password: SecretStr = Field(..., alias="DB_PASSWORD")
    db_name: str = Field(..., alias="DB_NAME")

    # --- GCS connection details ---
    # gcs_cred: Optional[str] = Field(default=None, alias="GCS_CRED")
    # bucket_name: Optional[str] = Field(default=None, alias="BUCKET_NAME")

    # --- API Gateway Authentication ---
    orchestration_server_auth_key: Optional[SecretStr] = Field(
        ..., alias="ORCHESTRATION_SERVER_AUTH_KEY"
    )
    api_gateway_url: Optional[str] = Field(default=None, alias="API_GATEWAY_URL")

    @computed_field
    @property
    def schema_file_path(self) -> Path:
        """
        Calculates and resolves the absolute path to the schema file
        relative to the project structure.
        """
        _project_root = Path(__file__).resolve().parent.parent.parent
        path = (
            _project_root / "app/shared/json_schemas/transition_schema.json"
        ).resolve()
        return path

    @computed_field
    @property
    def sqlalchemy_database_uri(self) -> str:
        """Build the PostgreSQL connection string."""
        password = self.db_password.get_secret_value() if self.db_password else ""
        return f"postgresql://{self.db_user}:{password}@{self.db_host}:{self.db_port}/{self.db_name}"

    # --- Pydantic-Settings configuration ---
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        populate_by_name=True,
        extra="ignore",
    )


try:
    settings = Settings()
except Exception as e:
    print(f"FATAL: Error loading configuration settings: {e}")
    raise

_project_root = Path(__file__).resolve().parent.parent.parent
SCHEMA_FILE_PATH: Path = (
    _project_root / "app/shared/json_schemas/transition_schema.json"
).resolve()
