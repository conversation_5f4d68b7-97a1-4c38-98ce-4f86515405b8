import asyncio
import json
import time
import traceback
from app.utils.helper_functions import format_execution_result
from app.utils.enhanced_logger import get_logger
from app.services.kafka_tool_executor import KafkaToolExecutor
from app.services.node_executor import NodeExecutor

logger = get_logger("TransitionHandler")


class TransitionHandler:

    def __init__(
        self,
        state_manager,
        transitions_by_id,
        nodes,
        dependency_map,
        workflow_utils,
        tool_executor,
        node_executor=None,
        agent_executor=None,
        result_callback=None,
        approval=False,
    ):
        self.state_manager = state_manager
        self.transitions_by_id = transitions_by_id
        self.nodes = nodes
        self.result_callback = result_callback
        self.dependency_map = dependency_map
        self.workflow_utils = workflow_utils
        self.tool_executor = tool_executor  # MCP server executor
        self.node_executor = node_executor  # Node executor for Components
        self.agent_executor = agent_executor  # Agent executor for Agent tasks

        # Flags
        self.current_transition_id = None
        self.approval = approval
        self.workflow_paused = False
        self._pause_event = asyncio.Event()

        self.logger = logger

        self.logger.info("TransitionHandler initialized")

    def _find_initial_transition(self):
        """
        Returns a list of transitions with transition_type='initial' if present,
        otherwise a list of all transitions sorted by sequence number.

        This allows for multiple initial transitions to be executed in parallel
        when multiple transitions have transition_type='initial'.
        """
        initial_transitions = [
            s
            for s in self.transitions_by_id.values()
            if s["transition_type"] == "initial"
        ]
        if initial_transitions:
            return initial_transitions

        # If no initial transitions found, use the transition with the smallest sequence number
        if self.transitions_by_id:
            return [
                sorted(self.transitions_by_id.values(), key=lambda s: s["sequence"])[0]
            ]

        raise Exception("No initial transitions found")

    async def _execute_transition_with_tracking(self, transition):
        """
        Execute a transition with proper tracking and monitoring to ensure it completes.
        This wrapper helps identify and debug issues with parallel execution.
        """

        transition_id = transition["id"]
        try:
            self.logger.info(
                f"Starting parallel execution of transition: {transition_id}"
            )
            result_info = {
                "result": f"Starting execution of transition: {transition_id}",
                "status": "started",
            }
            await self.result_callback(result_info)
            start_time = asyncio.get_event_loop().time()

            # Execute the transition
            result = await self._execute_standard_or_reflection_transition(transition)

            end_time = asyncio.get_event_loop().time()
            execution_time = end_time - start_time
            self.logger.info(
                f"Completed transition {transition_id} in {execution_time:.2f} seconds"
            )
            result_info = {
                "result": f"Completed transition in {execution_time:.2f} seconds",
                "status": "time_logged",
            }
            await self.result_callback(result_info)
            # Verify the transition was properly marked as completed
            if transition_id not in self.state_manager.completed_transitions:
                self.logger.warning(
                    f"Transition {transition_id} executed but not marked as completed!"
                )

            return result
        except Exception as e:
            self.logger.error(f"Exception in transition {transition_id}: {str(e)}")
            raise Exception(f"Exception in transition {transition_id}: {str(e)}")

    async def _execute_standard_or_reflection_transition(
        self, transition: dict, server_params_override=None, action_type=None
    ) -> list[str]:
        """
        Executes a 'standard' or 'reflection' transition.
        Assumes each transition in this function executes exactly one node.
        Returns the next transition IDs and the result of the execution.
        """
        transition_id = transition["id"]
        transition_type = transition["transition_type"]
        execution_type = transition.get(
            "execution_type", "MCP"
        )  # Default to MCP for backward compatibility
        self.logger.execute(
            f"Transition '{transition_id}' (type={transition_type}, execution_type={execution_type})"
        )

        node_info = transition.get("node_info", {})
        if not node_info:
            self.logger.error(
                f"No node_info specified for transition '{transition_id}'"
            )
            raise AttributeError(
                f"No node_info specified for transition '{transition_id}'"
            )

        node_id = node_info.get("node_id")
        tools_to_use = node_info.get("tools_to_use", [])
        output_data_configs = node_info.get("output_data", [])
        input_data_configs = node_info.get("input_data", [])
        next_transitions = []

        node_details = self.nodes.get(node_id)
        if not node_details:
            self.logger.error(f"Node details for '{node_id}' not found in schema")
            raise AttributeError(f"Node details for '{node_id}' not found in schema")

        server_script_path = node_details.get("server_script_path")
        if not server_script_path and execution_type != "Components":
            self.logger.error(f"No server script path for node '{node_id}'")
            raise AttributeError(f"No server script path for node '{node_id}'")

        server_tools_data = node_details.get("server_tools", [])
        if not server_tools_data:
            self.logger.error(f"No server tools defined for node '{node_id}'")
            raise AttributeError(f"No server tools defined for node '{node_id}'")

        tool_results = {}
        execution_result = None
        approval_required = transition.get("approval_required", False)

        # Determine which executor to use based on execution_type
        executor = self._get_executor_for_type(execution_type)
        if not executor:
            self.logger.error(
                f"No executor available for execution type: {execution_type}"
            )
            raise ValueError(
                f"No executor available for execution type: {execution_type}"
            )

        # Log which executor is being used
        if executor == self.node_executor:
            executor_type = "NodeExecutor"
        elif executor == self.agent_executor:
            executor_type = "AgentExecutor"
        else:
            executor_type = "KafkaToolExecutor"
        self.logger.info(f"Using {executor_type} for execution_type: {execution_type}")

        for tool_config in tools_to_use:
            tool_id = tool_config.get("tool_id")
            tool_name = tool_config.get("tool_name")
            tool_params_config = tool_config.get("tool_params", {"items": []}).get(
                "items"
            )

            node_tool_info = next(
                (tool for tool in server_tools_data if tool["tool_name"] == tool_name),
                None,
            )
            if not node_tool_info:
                self.logger.error(
                    f"Tool with name '{tool_name}' not found for node '{node_id}'"
                )
                node_tool_info = next(
                    (tool for tool in server_tools_data if tool["tool_id"] == tool_id),
                    None,
                )
                if not node_tool_info:
                    self.logger.error(
                        f"Tool with id '{tool_id}' not found for node '{node_id}'"
                    )
                    continue

            result_info = {
                "transition_id": transition_id,
                "node_id": node_id,
                "tool_name": tool_name,
                "result": None,
            }
            try:
                # Use universal handle-based parameter resolution
                tool_parameters = await self._resolve_tool_parameters_universally(
                    node_tool_info,
                    input_data_configs,
                    transition_id,
                    tool_params_config,
                    transition,
                )
                self.logger.debug(f"tool Parameters: {tool_parameters}")
                self.logger.info(
                    f"Invoking tool '{tool_name}' (tool_id: {tool_id}) for node '{node_id}' in transition '{transition_id}' with parameters: {tool_parameters}"
                )
                result_info["result"] = f"Connecting to server {node_id}"
                result_info["status"] = "connecting"
                await self.result_callback(result_info)
                if server_params_override:

                    def find_and_update_parameter(
                        param_dict, param_name, override_value, path=""
                    ):
                        """Recursively search for parameter in nested dictionaries and update if found."""
                        found = False

                        if isinstance(param_dict, dict):

                            if param_name in param_dict:
                                param_dict[param_name] = override_value
                                self.logger.debug(
                                    f"Parameter '{path+param_name}' for transition '{transition_id}' overridden with value: {override_value}"
                                )
                                found = True

                            for key, value in param_dict.items():
                                if isinstance(value, dict):
                                    nested_found = find_and_update_parameter(
                                        value,
                                        param_name,
                                        override_value,
                                        f"{path}{key}.",
                                    )
                                    found = found or nested_found

                        return found

                    for param_name, override_value in server_params_override.items():
                        found = find_and_update_parameter(
                            tool_parameters, param_name, override_value
                        )

                        if not found:
                            self.logger.warning(
                                f"Parameter '{param_name}' from server_params_override not found in tool_parameters for transition '{transition_id}'. Ignoring override."
                            )

                # Execute the tool using the appropriate executor based on execution_type
                if executor == self.tool_executor:
                    # For MCP executor, pass the node_id as mcp_id
                    execution_result = await executor.execute_tool(
                        server_script_path, tool_name, tool_parameters, mcp_id=node_id
                    )
                elif executor == self.agent_executor:
                    # For Agent executor, pass the node_id as agent_id
                    execution_result = await executor.execute_tool(
                        server_script_path, tool_name, tool_parameters, agent_id=node_id
                    )
                else:
                    # For other executors (like NodeExecutor), use the original signature
                    execution_result = await executor.execute_tool(
                        server_script_path, tool_name, tool_parameters
                    )

                # Log the execution result for debugging
                self.logger.info(
                    f"Execution result from {execution_type} executor: {json.dumps(execution_result, indent=2)}"
                )

                # Check for errors in the execution result
                if isinstance(execution_result, dict):
                    # Log the execution result for debugging
                    self.logger.info(
                        f"Checking execution result for errors: {json.dumps(execution_result, indent=2)}"
                    )

                    # Check if the status is error
                    if execution_result.get("status") == "error":
                        # Handle different error formats from different executors
                        error_message = None

                        # Format 1: Direct error message in 'error' field (from kafka_tool_executor.py)
                        if "error" in execution_result:
                            error_message = execution_result.get("error")
                            self.logger.info(
                                f"Found direct error in execution result: {error_message}"
                            )

                        # Format 2: Error message in nested 'result.error' field (from node_executor.py)
                        elif isinstance(execution_result.get("result"), dict):
                            result_dict = execution_result["result"]
                            self.logger.info(
                                f"Processing result dictionary: {json.dumps(result_dict, indent=2)}"
                            )

                            # Extract error message from result.error
                            if "error" in result_dict:
                                error_message = result_dict.get("error")
                                self.logger.info(
                                    f"Found error in result dictionary: {error_message}"
                                )

                                # Include additional error details if available
                                if "data" in result_dict and isinstance(
                                    result_dict["data"], dict
                                ):
                                    error_details = result_dict["data"].get("detail")
                                    if error_details:
                                        error_message = (
                                            f"{error_message} - {error_details}"
                                        )
                                        self.logger.info(
                                            f"Added error details: {error_message}"
                                        )

                                # Include status code if available
                                if "status_code" in result_dict:
                                    status_code = result_dict.get("status_code")
                                    if (
                                        status_code
                                        and not str(status_code) in error_message
                                    ):
                                        error_message = f"{error_message} (Status Code: {status_code})"
                                        self.logger.info(
                                            f"Added status code: {error_message}"
                                        )

                        # Fallback if no specific error format is matched
                        else:
                            error_message = execution_result.get(
                                "result", "Unknown error occurred"
                            )
                            self.logger.info(
                                f"Using fallback error message: {error_message}"
                            )

                        if error_message:
                            self.logger.error(
                                f"Execution failed with error: {error_message}"
                            )
                            # Force an exception to be raised for any error
                            raise Exception(f"Tool execution error: {error_message}")
                        else:
                            self.logger.error(
                                f"Execution failed but no error message could be extracted: {json.dumps(execution_result, indent=2)}"
                            )
                            raise Exception(
                                f"Tool execution error: Unknown error occurred"
                            )

                    # Special handling for Components execution type
                    elif execution_type == "Components" and isinstance(
                        execution_result.get("result"), dict
                    ):
                        result_dict = execution_result["result"]
                        # Check if there's an error field in the result dict even if status is not 'error'
                        if "error" in result_dict:
                            error_message = result_dict.get("error")
                            self.logger.info(
                                f"Found error in Components result: {error_message}"
                            )

                            # Include additional error details if available
                            if "data" in result_dict and isinstance(
                                result_dict["data"], dict
                            ):
                                error_details = result_dict["data"].get("detail")
                                if error_details:
                                    error_message = f"{error_message} - {error_details}"

                            # Include status code if available
                            if "status_code" in result_dict:
                                status_code = result_dict.get("status_code")
                                if (
                                    status_code
                                    and not str(status_code) in error_message
                                ):
                                    error_message = (
                                        f"{error_message} (Status Code: {status_code})"
                                    )

                            self.logger.error(
                                f"Components execution failed with error: {error_message}"
                            )
                            raise Exception(f"Tool execution error: {error_message}")

                # Wrap execution result in the expected structure for handle mapping compatibility
                wrapped_result = {
                    "transition_id": transition_id,
                    "node_id": node_id,
                    "tool_name": tool_name,
                    "result": {"result": execution_result},
                    "status": "completed",
                    "timestamp": time.time(),
                }
                tool_results[tool_name] = wrapped_result

                if execution_type == "MCP":
                    output_schema = node_tool_info.get("output_schema", {})

                    try:
                        execution_result_parsed = execution_result
                    except json.JSONDecodeError as e:
                        self.logger.error(f"JSONDecodeError DETECTED: {e}")
                        execution_result_parsed = []

                    formatted_result = format_execution_result(
                        output_schema, execution_result_parsed
                    )
                    # Log semantic type inclusion for debugging
                    self.logger.debug(
                        f"Formatted result with semantic types: {formatted_result}"
                    )
                else:
                    formatted_result = execution_result

                if self.result_callback:
                    try:
                        result_info["result"] = formatted_result
                        result_info["status"] = "completed"
                        result_info["approval_required"] = (
                            True if approval_required and self.approval else False
                        )
                        await self.result_callback(result_info)
                        if approval_required and self.approval:
                            self.state_manager.workflow_paused = True
                            self.logger.info(
                                f"Transition '{transition_id}' completed. Workflow paused, waiting for approval..."
                            )
                            result_info["result"] = (
                                "Workflow paused, waiting for approval..."
                            )
                            result_info["status"] = "paused"
                            await self.result_callback(result_info)
                            self.state_manager.transitions_waiting_for_approval.append(
                                transition_id
                            )
                            self._pause_event.clear()
                            await self._pause_event.wait()
                            self.logger.info(
                                f"Workflow resumed after approval for transition '{transition_id}'."
                            )
                            del self.state_manager.transitions_waiting_for_approval[
                                transition_id
                            ]
                            self.state_manager.workflow_paused = False
                    except Exception as callback_error:
                        self.logger.error(
                            f"Result callback function raised an exception: {callback_error}"
                        )
                if action_type == "regenerate":
                    return []

            except Exception as e:
                self.logger.error(
                    f"Tool execution failed for tool '{tool_name}' (tool_id: {tool_id}) in node '{node_id}' of transition '{transition_id}': {str(e)}"
                    + traceback.format_exc()
                )
                error_message = f"[ERROR] Tool Execution Failed with error: {str(e)}"
                tool_results[tool_name] = error_message
                result_info["result"] = error_message
                result_info["status"] = "failed"
                if self.result_callback:
                    await self.result_callback(result_info)
                raise Exception(f"Tool execution error: {error_message}")

        self.state_manager.mark_transition_completed(transition_id, tool_results)

        chosen_next_transitions = []
        conditional_routing = transition.get("conditional_routing")
        conditional_nodes = set()

        if conditional_routing:
            chosen_next_transitions = await self.workflow_utils._evaluate_switch_case(
                transition_routing=conditional_routing, node_result=execution_result
            )
            self.logger.info(
                f"Switch case evaluation returned transitions: {chosen_next_transitions}"
            )
            if conditional_routing.get("cases"):
                for case in conditional_routing["cases"]:
                    conditional_nodes.add(case.get("next_transition"))

        transition_output_transitions = [
            config.get("to_transition_id")
            for config in output_data_configs
            if config.get("to_transition_id")
        ]
        final_next_transitions = set()

        # Add all transitions from switch case evaluation
        for transition_id in chosen_next_transitions:
            if transition_id:  # Ensure we don't add None values
                final_next_transitions.add(transition_id)

        for output_transition in transition_output_transitions:
            if output_transition not in conditional_nodes:
                final_next_transitions.add(output_transition)

        next_transitions.extend(list(final_next_transitions))

        return next_transitions

    async def _resolve_tool_parameters_universally(
        self,
        node_tool_info: dict,
        input_data_configs: list,
        transition_id: str,
        tool_params_config: dict,
        transition: dict,
    ) -> dict:
        """
        Universal tool parameter resolution using handle-based system.

        This method uses the new handle-based data propagation system to resolve
        tool parameters without hardcoded patterns or placeholder interpretation.

        Args:
            node_tool_info: Tool information from node definition
            input_data_configs: Input data configurations with handle mappings
            transition_id: Current transition ID
            tool_params_config: Tool parameter configuration
            transition: Complete transition object with result_resolution metadata

        Returns:
            dict: Resolved tool parameters
        """
        self.logger.debug(
            f"🔧 Starting universal parameter resolution for transition: {transition_id}"
        )

        # Check if we have result_resolution metadata for enhanced resolution
        result_resolution = transition.get("result_resolution", {})
        if result_resolution:
            self.logger.debug(
                f"📊 Using result_resolution metadata: {result_resolution.get('node_type', 'unknown')}"
            )

            # Use enhanced handle-based resolution with result_resolution metadata
            return await self._resolve_with_result_resolution(
                node_tool_info,
                input_data_configs,
                transition_id,
                tool_params_config,
                result_resolution,
            )
        else:
            self.logger.debug(
                "⚠️ No result_resolution metadata found, falling back to standard handle resolution"
            )

            # Fallback to standard handle-based resolution
            return await self.workflow_utils._format_tool_parameters(
                node_tool_info,
                input_data_configs,
                transition_id,
                tool_params_config,
            )

    async def _resolve_with_result_resolution(
        self,
        node_tool_info: dict,
        input_data_configs: list,
        transition_id: str,
        tool_params_config: dict,
        result_resolution: dict,
    ) -> dict:
        """
        Enhanced parameter resolution using result_resolution metadata.

        This method uses the complete result_resolution system to provide
        the most accurate parameter resolution possible.

        Args:
            node_tool_info: Tool information from node definition
            input_data_configs: Input data configurations with handle mappings
            transition_id: Current transition ID
            tool_params_config: Tool parameter configuration
            result_resolution: Result resolution metadata from transition

        Returns:
            dict: Resolved tool parameters with enhanced accuracy
        """
        self.logger.debug(
            f"🎯 Enhanced parameter resolution for {result_resolution.get('node_type', 'unknown')} node"
        )

        # Collect all previous results
        all_previous_results = {}
        if input_data_configs:
            for input_data_config in input_data_configs:
                from_transition_id = input_data_config.get("from_transition_id")
                if from_transition_id:
                    result_from_dependency = self.state_manager.get_transition_result(
                        from_transition_id
                    )
                    if result_from_dependency:
                        all_previous_results[from_transition_id] = (
                            result_from_dependency
                        )
                        self.logger.debug(
                            f"📥 Collected results from transition {from_transition_id}"
                        )

        # If no previous results, convert current params to dict format
        if not all_previous_results:
            self.logger.debug("📝 No previous results found, using static parameters")
            return self.workflow_utils._convert_params_to_dict(tool_params_config)

        # Extract handle mappings from input data configs
        handle_mappings = self.workflow_utils._extract_handle_mappings(
            input_data_configs
        )

        if not handle_mappings:
            self.logger.debug(
                "🔄 No handle mappings found, falling back to standard resolution"
            )
            return await self.workflow_utils._format_tool_parameters(
                node_tool_info,
                input_data_configs,
                transition_id,
                tool_params_config,
            )

        # Validate handle mapping compatibility
        validation_report = self.workflow_utils.validate_handle_mapping_compatibility(
            handle_mappings, all_previous_results
        )

        self.logger.info(
            f"🔍 Handle validation: {validation_report['overall_compatibility']} "
            f"({validation_report['compatible_mappings']}/{validation_report['total_mappings']} compatible)"
        )

        # Create universal parameter mapping
        parameter_mapping = self.workflow_utils.create_universal_parameter_mapping(
            handle_mappings, all_previous_results
        )

        resolved_parameters = parameter_mapping["resolved_parameters"]
        mapping_metadata = parameter_mapping["mapping_metadata"]

        # Log detailed mapping results
        self.logger.info(
            f"🎯 Parameter mapping complete: {mapping_metadata['successful_mappings']}/{mapping_metadata['total_mappings']} successful"
        )

        # If we have failed mappings, log details for debugging
        if mapping_metadata["failed_mappings"] > 0:
            failed_details = [
                detail
                for detail in mapping_metadata["mapping_details"]
                if detail["status"] in ["failed", "error"]
            ]
            for detail in failed_details:
                self.logger.warning(
                    f"❌ Failed mapping: {detail['source_handle_id']} → {detail['target_handle_id']} "
                    f"(Error: {detail.get('error', 'Unknown')})"
                )

        # Merge with static parameters that don't have handle mappings
        current_params_dict = self.workflow_utils._convert_params_to_dict(
            tool_params_config
        )
        for param_name, param_value in current_params_dict.items():
            if param_name not in resolved_parameters:
                # Only include if it's not a placeholder (no ${...} pattern)
                if not (
                    isinstance(param_value, str)
                    and "${" in param_value
                    and "}" in param_value
                ):
                    resolved_parameters[param_name] = param_value
                    self.logger.debug(
                        f"📌 Added static parameter: {param_name} = {param_value}"
                    )

        self.logger.debug(f"✅ Final resolved parameters: {resolved_parameters}")
        return resolved_parameters

    def _get_executor_for_type(self, execution_type):
        """
        Returns the appropriate executor based on the execution type.
        """
        # MCP server executor types
        if execution_type == "MCP":
            return self.tool_executor

        # Node executor types
        elif execution_type == "Components":
            if self.node_executor:
                return self.node_executor
            else:
                self.logger.warning(
                    f"Node executor not available for execution type: {execution_type}. Falling back to MCP executor."
                )
                return self.tool_executor

        # Agent executor types
        elif execution_type == "agent":
            if self.agent_executor:
                return self.agent_executor
            else:
                self.logger.warning(
                    f"Agent executor not available for execution type: {execution_type}. Falling back to MCP executor."
                )
                return self.tool_executor
        else:
            self.logger.warning(
                f"Unknown execution type: {execution_type}. Falling back to MCP executor."
            )
            return self.tool_executor

    async def _handle_reflection_logic(self, transition: dict) -> list[str]:
        """
        Handles reflection logic.
        """
        reflection_info = transition.get("reflection", {})
        next_transition_candidates = []

        if not reflection_info:
            next_transition_candidates = (
                await self._execute_standard_or_reflection_transition(transition)
            )
            return next_transition_candidates

        iteration_count = reflection_info.get("iteration_count", 0) + 1
        max_iterations = reflection_info.get("max_iterations", 1)
        reflection_info["iteration_count"] = iteration_count

        self.logger.info(
            f"[REFLECTION] transition '{transition['id']}' iteration {iteration_count}/{max_iterations}."
        )

        if iteration_count > max_iterations:
            self.logger.info(
                f"Max iterations {max_iterations} reached. Reflection ends."
            )
            next_transition_candidates = (
                await self._execute_standard_or_reflection_transition(transition)
            )
            return next_transition_candidates

        # Execute the reflection logic
        next_transition_candidates = (
            await self._execute_standard_or_reflection_transition(transition)
        )
        return next_transition_candidates

    def _resolve_next_transition(self, next_transition_candidates):
        """
        Resolve next transitions from transition candidates, prioritizing reflection transitions and then handling others based on closest sequence.
        Now directly handles a list of transition IDs as input (next_transition_candidates).
        Returns a list of ALL transition IDs to be executed next.
        Resolution logic:
        1. Prioritize reflection transitions.
        2. For non-reflection transitions, choose based on closest *higher* sequence number than current_transition.
        """
        if not next_transition_candidates:
            self.logger.info(
                "No next transition candidates provided for resolution. Returning empty list."
            )
            return []

        reflection_transitions = []
        non_reflection_transitions = []
        resolved_transition_ids = set()

        for transition_id in next_transition_candidates:
            if transition_id in resolved_transition_ids:
                continue

            transition = self.transitions_by_id.get(transition_id)
            if not transition:
                self.logger.warning(
                    f"Transition ID '{transition_id}' from candidates not found in transitions_by_id."
                )
                continue

            if transition["transition_type"] == "reflection":
                reflection_transitions.append(transition_id)
            else:
                non_reflection_transitions.append(transition_id)
            resolved_transition_ids.add(transition_id)

        prioritized_transitions = reflection_transitions + non_reflection_transitions
        self.logger.info(
            f"Resolved next transitions (direct transition IDs): {prioritized_transitions}"
        )
        return prioritized_transitions

    async def regenerate_transition(
        self, transition_id, action_type, server_params_override=None
    ):
        """
        Regenerates a specific transition (server) and its dependent transitions.
        Resets the state for the transition and re-executes it, then returns.
        Does NOT automatically resume the entire workflow execution flow.
        """
        if transition_id not in self.transitions_by_id:
            self.logger.error(
                f"Transition ID '{transition_id}' not found for regeneration: {transition_id}"
            )
            return False

        self.logger.info(f"Initiating regeneration for transition: {transition_id}")

        if action_type == "re-execute":
            # 1. Reset workflow state for the given transition and its dependents
            reset_success = self.state_manager.reset_to_transition(
                transition_id, self.transitions_by_id, self.dependency_map
            )
            if not reset_success:
                self.logger.error(
                    f"State reset failed for transition {transition_id}, regeneration cannot proceed."
                )
                return False

        # 2. Get the transition object
        transition_to_regenerate = self.transitions_by_id[transition_id]

        original_approval_flag = self.approval
        self.approval = False

        # 3. Execute the transition directly, passing server_params_override
        result = await self._execute_standard_or_reflection_transition(
            transition_to_regenerate,
            server_params_override=server_params_override,
            action_type="regenerate",
        )

        self.approval = original_approval_flag

        if result is not None:
            self.logger.info(f"Regeneration completed for transition: {transition_id}")
            return True
        else:
            self.logger.error(f"Regeneration failed for transition: {transition_id}")
            return False
