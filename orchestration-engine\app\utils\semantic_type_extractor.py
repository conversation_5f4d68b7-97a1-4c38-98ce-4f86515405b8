"""
Semantic Type Extraction Utility

This module provides utilities for extracting semantic type information from output schema
field definitions. It extracts semantic types from the 'format' field in data_type objects
and provides fallback to 'string' when format is empty or missing.

The semantic types help frontend applications render elements appropriately based on the
data's semantic meaning (e.g., email, url, datetime, etc.).
"""

from typing import Dict, Any, Optional
import logging

# Try to import enhanced logger, fall back to standard logging for testing
try:
    from app.utils.enhanced_logger import get_logger

    logger = get_logger("SemanticTypeExtractor")
except Exception:
    logger = logging.getLogger("SemanticTypeExtractor")


def extract_semantic_type(field_definition: Dict[str, Any]) -> str:
    """
    Extract semantic type from field definition format field.

    This function looks for semantic type information in the field definition's
    data_type.format field. If no format is specified or the format is empty,
    it returns 'string' as the default semantic type.

    Args:
        field_definition: Field definition dictionary containing data_type information
                         Expected structure: {
                             "field_name": "example_field",
                             "data_type": {
                                 "type": "string",
                                 "format": "email",  # This is what we extract
                                 "description": "..."
                             }
                         }

    Returns:
        str: Semantic type extracted from format field, or "string" as default

    Examples:
        >>> field_def = {
        ...     "field_name": "user_email",
        ...     "data_type": {"type": "string", "format": "email"}
        ... }
        >>> extract_semantic_type(field_def)
        'email'

        >>> field_def = {
        ...     "field_name": "description",
        ...     "data_type": {"type": "string"}
        ... }
        >>> extract_semantic_type(field_def)
        'string'
    """
    if not isinstance(field_definition, dict):
        logger.warning(
            f"Invalid field_definition type: {type(field_definition)}. Expected dict."
        )
        return "string"

    data_type = field_definition.get("data_type", {})
    if not isinstance(data_type, dict):
        logger.warning(
            f"Invalid data_type in field_definition: {type(data_type)}. Expected dict."
        )
        return "string"

    format_value = data_type.get("format")

    if format_value and isinstance(format_value, str):
        semantic_type = format_value.strip().lower()
        if semantic_type:
            logger.debug(f"Extracted semantic type '{semantic_type}' from format field")
            return semantic_type

    # Log when using default
    field_name = field_definition.get("field_name", "unknown")
    logger.debug(
        f"No valid format found for field '{field_name}', using default semantic type 'string'"
    )
    return "string"


def extract_semantic_type_for_nested(
    field_definition: Dict[str, Any], data_type: str
) -> str:
    """
    Extract semantic type for nested data structures (arrays, objects).

    For complex data types like arrays and objects, this function attempts to extract
    semantic type information while considering the nested structure.

    Args:
        field_definition: Field definition dictionary
        data_type: The primary data type ("array", "object", etc.)

    Returns:
        str: Semantic type for the nested structure
    """
    if not isinstance(field_definition, dict):
        logger.warning(
            f"Invalid field_definition type for nested extraction: {type(field_definition)}"
        )
        return "string"

    # For arrays, check if there's semantic information about the array items
    if data_type == "array":
        data_type_info = field_definition.get("data_type", {})
        items_info = data_type_info.get("items", {})

        # Check if items have format information
        if isinstance(items_info, dict) and "format" in items_info:
            item_format = items_info.get("format")
            if item_format and isinstance(item_format, str):
                semantic_type = item_format.strip().lower()
                if semantic_type:
                    logger.debug(
                        f"Extracted semantic type '{semantic_type}' for array items"
                    )
                    return f"array_of_{semantic_type}"

        # Check if the array itself has format information
        array_format = data_type_info.get("format")
        if array_format and isinstance(array_format, str):
            semantic_type = array_format.strip().lower()
            if semantic_type:
                logger.debug(f"Extracted semantic type '{semantic_type}' for array")
                return semantic_type

        return "array"

    # For objects, check if there's semantic information about the object
    elif data_type == "object":
        data_type_info = field_definition.get("data_type", {})
        object_format = data_type_info.get("format")

        if object_format and isinstance(object_format, str):
            semantic_type = object_format.strip().lower()
            if semantic_type:
                logger.debug(f"Extracted semantic type '{semantic_type}' for object")
                return semantic_type

        return "object"

    # For other types, fall back to standard extraction
    return extract_semantic_type(field_definition)


def get_supported_semantic_types() -> Dict[str, list]:
    """
    Get a dictionary of all supported semantic types organized by category.

    Returns:
        Dict[str, list]: Dictionary with categories as keys and lists of semantic types as values
    """
    return {
        "communication": ["email", "url", "link", "uri", "href"],
        "datetime": [
            "datetime",
            "date",
            "time",
            "timestamp",
            "created",
            "updated",
            "modified",
        ],
        "media": [
            "audio",
            "video",
            "image",
            "audio_url",
            "video_url",
            "image_url",
            "audio_file",
            "video_file",
            "image_file",
        ],
        "system": ["file_path", "identifier", "id", "uuid", "guid", "status", "color"],
        "numeric": ["currency", "money", "percentage", "percent"],
        "default": ["string", "text"],
    }


def validate_semantic_type(semantic_type: str) -> bool:
    """
    Validate if a semantic type is supported.

    Args:
        semantic_type: The semantic type to validate

    Returns:
        bool: True if the semantic type is supported, False otherwise
    """
    if not isinstance(semantic_type, str):
        return False

    supported_types = get_supported_semantic_types()
    semantic_type_lower = semantic_type.lower().strip()

    for category_types in supported_types.values():
        if semantic_type_lower in category_types:
            return True

    return False


def normalize_semantic_type(semantic_type: str) -> str:
    """
    Normalize a semantic type to a standard format.

    This function handles common variations and aliases for semantic types.

    Args:
        semantic_type: The semantic type to normalize

    Returns:
        str: Normalized semantic type
    """
    if not isinstance(semantic_type, str):
        return "string"

    normalized = semantic_type.lower().strip()

    # Handle common aliases
    aliases = {
        "uri": "url",
        "href": "url",
        "link": "url",
        "mail": "email",
        "e-mail": "email",
        "timestamp": "datetime",
        "created": "datetime",
        "updated": "datetime",
        "modified": "datetime",
        "money": "currency",
        "percent": "percentage",
        "id": "identifier",
        "uuid": "identifier",
        "guid": "identifier",
        "text": "string",
    }

    return aliases.get(normalized, normalized)
