# Data Propagation Fix Task List

## Overview

This document outlines the comprehensive task list to fix the critical data propagation issue in the orchestration engine. The problem involves how mapping information from workflow edges is captured in the transition schema and utilized for parameter resolution.

## Critical Issue Analysis

**Current Problems:**

1. **Placeholder System Failure**: `${field_name}` placeholders map to output schema names, but actual results are nested under `result` causing resolution failures
2. **Hardcoded Patterns**: Current code has hardcoded logic for specific node types instead of universal handle-based resolution
3. **Missing Handle Mapping**: Workflow edges contain explicit `sourceHandle`/`targetHandle` mappings that are ignored
4. **No Backward Compatibility Needed**: We're redesigning the system to be handle-centric and universal

**New Approach:**

- **Handle-Based Resolution**: Use explicit handle IDs/names for direct field mapping
- **Universal Design**: Works with any node type (MCP, Component, API, Custom) as long as it has handles
- **Explicit Mapping**: Direct handle-to-handle connections without placeholder interpretation
- **Result Path Resolution**: Smart resolution of nested result structures based on node output patterns

---

## Phase 1: Design Universal Handle-Based Mapping System

### Task 1.1: Create handle-based mapping structure in transition_schema.json

- [x] Replace placeholder system with direct handle mapping structure
- [x] Add `handle_mappings` array to `input_data` entries with fields:
  - `source_transition_id`: ID of the source transition
  - `source_handle_id`: Exact handle ID from source node output
  - `target_handle_id`: Exact handle ID for target node input
  - `edge_id`: Reference to original workflow edge for traceability
- [x] Remove dependency on field name matching and placeholder resolution
- [x] Design schema to work universally with any node type

### Task 1.2: Add universal result resolution metadata

- [x] Add `result_resolution` structure to capture how to extract data from node results
- [x] Include `output_handle_registry` mapping handle IDs to actual result paths
- [x] Support dynamic result path discovery for unknown node types
- [x] Add handle validation and conflict resolution mechanisms
- [x] Design system to work with single/multiple output handles

---

## Phase 2: Implement Universal Workflow Schema Converter

### Task 2.1: Create universal handle extraction system

- [x] Remove all hardcoded node type logic and pattern matching
- [x] Implement universal handle discovery from any node definition
- [x] Extract `sourceHandle` and `targetHandle` directly from workflow edges
- [x] Create handle registry that maps handle IDs to their definitions
- [x] Build universal node output/input discovery system

### Task 2.2: Implement direct handle-to-handle mapping

- [x] Replace `create_enhanced_field_mapping` with direct handle mapping
- [x] Generate `handle_mappings` arrays directly from workflow edges
- [x] Map `edge.sourceHandle` → `source_handle_id` and `edge.targetHandle` → `target_handle_id`
- [x] Remove all placeholder generation and field name guessing logic
- [x] Create one-to-one handle mapping without interpretation

### Task 2.3: Build universal result path resolver

- [x] Create system to dynamically discover how to extract data from any node result
- [x] Implement result structure analysis for unknown node types
- [x] Build handle-to-result-path mapping system
- [x] Remove hardcoded result extraction patterns
- [x] Support both single and multiple output handle scenarios

---

## Phase 3: Replace Parameter Resolution with Handle-Based System

### Task 3.1: Replace _format_tool_parameters with handle-based resolution

- [x] Remove placeholder system (`${field_name}`) completely
- [x] Implement direct handle-based parameter resolution
- [x] Use `handle_mappings` from transition schema for direct data routing
- [x] Remove all field name guessing and AI-based fallback logic
- [x] Create universal parameter assembly using handle connections

### Task 3.2: Implement handle-based data extraction

- [x] Replace `extract_explicit_mappings` with `extract_handle_mappings`
- [x] Create `resolve_handle_data` method that uses handle IDs to extract exact data
- [x] Remove `_process_params_for_placeholders` and replace with direct handle resolution
- [x] Implement universal data extraction that works with any result structure
- [x] Add handle validation and error reporting

### Task 3.3: Build universal result-to-parameter mapping system

- [x] Create system that maps handle IDs directly to parameter values
- [x] Remove dependency on field name matching or pattern recognition
- [x] Implement dynamic result structure navigation using handle metadata
- [x] Support any node type without hardcoded logic
- [x] Handle single output scenarios where result structure varies

---

## Phase 4: Implement Universal Handle Resolution

### Task 4.1: Create universal handle-based workflow execution

- [x] Remove all hardcoded workflow pattern logic
- [x] Implement universal handle resolution that works with any workflow structure
- [x] Test with complex multi-node workflows using only handle connections
- [x] Validate that any node type can connect to any other node type via handles
- [x] Ensure system works regardless of node sequence or complexity

### Task 4.2: Implement dynamic result structure handling

- [x] Create system that can handle any result structure from any node type
- [x] Remove assumptions about result nesting (e.g., `result.result` patterns)
- [x] Implement dynamic discovery of where handle data is located in results
- [x] Support nodes with single outputs that may have varying result structures
- [x] Handle edge cases where result format differs from expected schema

### Task 4.3: Build comprehensive handle validation system

- [x] Create handle connection validation for any workflow
- [x] Implement handle compatibility checking between any node types
- [x] Add comprehensive error reporting for handle resolution failures
- [x] Create debugging tools for handle mapping issues
- [x] Implement handle conflict resolution for complex workflows

---

## Phase 5: Testing and Validation

### Task 5.1: Create comprehensive test cases

- [x] Test the sample_workflow.json conversion end-to-end
- [x] Validate all edge mappings are properly captured in transition schema
- [x] Test parameter resolution with complex nested structures
- [x] Create unit tests for each mapping function
- [x] Add integration tests for complete workflow execution

### Task 5.2: Test universal system capabilities

- [x] Test with workflows containing only MCP nodes
- [x] Test with workflows containing only Component nodes
- [x] Test with mixed node type workflows (MCP + Component + API + Custom)
- [x] Validate system works with unknown/future node types
- [x] Test with workflows of varying complexity (2 nodes to 50+ nodes)

### Task 5.3: Performance and error handling

- [x] Add proper error handling for malformed handle mappings
- [x] Implement comprehensive logging for handle resolution debugging
- [x] Ensure efficient handle lookup for large workflows
- [x] Add performance benchmarks for handle-based resolution
- [x] Implement timeout handling for complex handle resolution scenarios

---

## Phase 6: Documentation and Integration

### Task 6.1: Update schema documentation

- [ ] Document the new handle-based mapping structure in transition schema
- [ ] Add examples of handle connections for different node types
- [ ] Update parameter resolution documentation with handle-based approach
- [ ] Create troubleshooting guide for handle mapping issues
- [ ] Add best practices for universal workflow design

### Task 6.2: Integration testing

- [ ] Test with real workflow scenarios using handle-based system
- [ ] Validate with any node types and handle patterns
- [ ] Ensure proper error propagation and logging for handle issues
- [ ] Test with various workflow complexities using universal system
- [ ] Validate performance under load with handle-based resolution

---

## Priority Matrix

### 🔴 High Priority (Critical Path)

- [ ] Task 1.1: Create handle-based mapping structure in transition_schema.json
- [ ] Task 2.2: Implement direct handle-to-handle mapping
- [ ] Task 3.1: Replace _format_tool_parameters with handle-based resolution

### 🟡 Medium Priority (Core Features)

- [ ] Task 1.2: Add universal result resolution metadata
- [ ] Task 2.1: Create universal handle extraction system
- [ ] Task 3.2: Implement handle-based data extraction

### 🟢 Low Priority (Enhancement & Testing)

- [ ] All Phase 4 tasks (Universal handle resolution)
- [ ] All Phase 5 tasks (Testing and validation)
- [ ] All Phase 6 tasks (Documentation and integration)

---

## Success Criteria

- [ ] Any workflow with handle-based connections executes successfully
- [ ] All handle mappings are properly captured and utilized without interpretation
- [ ] Parameter resolution works universally for any node type and result structure
- [ ] System works with unknown/future node types without code changes
- [ ] Performance meets or exceeds current benchmarks with handle-based resolution
- [ ] Complete removal of placeholder system and hardcoded patterns

---

## Related Files

- `ruh.ai/workflow-service/testing/sample_workflow.json` - Test workflow with complex mappings
- `ruh_catalyst/orchestration-engine/app/shared/json_schemas/transition_schema.json` - Schema definition
- `ruh.ai/workflow-service/app/services/workflow_builder/workflow_schema_converter.py` - Conversion logic
- `ruh_catalyst/orchestration-engine/app/core_/workflow_utils.py` - Parameter resolution logic

✅ Guidelines for Completing the Task List

1. Always Follow TDD (Test-Driven Development)
    Start by writing failing test cases that clearly define the expected behavior of the feature or fix.

    Only then begin implementing the actual functionality.

    Ensure all tests pass before considering the task complete.

    Write both unit and, where needed, integration tests.

2. Respect the File and Folder Structure
    Never create files or directories arbitrarily.

    Locate the appropriate existing module or directory that logically relates to the feature you are building.

    If unsure where something should go:

    Check where similar components already exist.
