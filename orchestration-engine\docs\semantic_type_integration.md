# Semantic Type Integration Documentation

## Overview

The orchestration engine now supports semantic type extraction from output schemas and includes this information in the result format sent to the frontend. This enhancement enables better element rendering and user experience by providing semantic context for data fields.

## Enhanced Result Format

### Previous Format
```json
{
  "data": "some value",
  "data_type": "primitive data type",
  "property_name": "field_name"
}
```

### New Enhanced Format
```json
{
  "data": "some value",
  "data_type": "primitive data type", 
  "property_name": "field_name",
  "semantic_type": "email|url|datetime|string|etc"
}
```

## Semantic Type Extraction

### Source of Semantic Types
Semantic types are extracted from the `format` field in output schema definitions:

```json
{
  "predefined_fields": [
    {
      "field_name": "user_email",
      "data_type": {
        "type": "string",
        "format": "email",
        "description": "User email address"
      }
    }
  ]
}
```

### Default Behavior
- When `format` field is present and valid: Uses the format value as semantic type
- When `format` field is empty, missing, or invalid: Defaults to `"string"`
- Case insensitive: `"EMAIL"` becomes `"email"`
- Whitespace handling: `"  email  "` becomes `"email"`

## Supported Semantic Types

### Communication Types
- `email` - Email addresses
- `url` - Web URLs and links  
- `link` - Alternative for URL
- `uri` - Alternative for URL
- `href` - Alternative for URL

### DateTime Types
- `datetime` - Date and time fields
- `date` - Date only fields
- `time` - Time only fields
- `timestamp` - Timestamp fields
- `created` - Creation timestamps
- `updated` - Update timestamps
- `modified` - Modification timestamps

### Media Types
- `audio` - Audio content
- `video` - Video content
- `image` - Image content
- `audio_url` - Audio file URLs
- `video_url` - Video file URLs
- `image_url` - Image file URLs
- `audio_file` - Audio file paths
- `video_file` - Video file paths
- `image_file` - Image file paths

### System Types
- `file_path` - File system paths
- `identifier` - ID fields
- `id` - Alternative for identifier
- `uuid` - UUID identifiers
- `guid` - GUID identifiers
- `status` - Status indicators
- `color` - Color codes and values

### Numeric Types
- `currency` - Monetary values
- `money` - Alternative for currency
- `percentage` - Percentage values
- `percent` - Alternative for percentage

### Default Type
- `string` - Default for all unspecified or unknown types

## Implementation Details

### Core Components

#### 1. Semantic Type Extractor (`app/utils/semantic_type_extractor.py`)
- `extract_semantic_type(field_definition)` - Extracts semantic type from field definition
- `extract_semantic_type_for_nested(field_definition, data_type)` - Handles arrays and objects
- `validate_semantic_type(semantic_type)` - Validates semantic type
- `normalize_semantic_type(semantic_type)` - Normalizes semantic type aliases

#### 2. Enhanced Helper Functions (`app/utils/helper_functions.py`)
- Modified `format_execution_result()` to include semantic types
- Updated `process_item()` to extract and include semantic types
- Handles all data types: simple, arrays, objects, nested structures

#### 3. Transition Handler Integration (`app/core_/transition_handler.py`)
- Semantic type information flows through to result callback
- Enhanced result format passed to frontend
- Logging for semantic type inclusion

### Data Flow

1. **Schema Definition**: Output schema contains `format` field with semantic type
2. **Execution**: Tool execution returns data
3. **Formatting**: `format_execution_result()` processes data with schema
4. **Extraction**: Semantic types extracted from `format` fields
5. **Enhancement**: Result format enhanced with semantic types
6. **Callback**: Enhanced result sent to frontend via result callback

## Usage Examples

### Basic Email Field
```json
// Schema
{
  "field_name": "user_email",
  "data_type": {
    "type": "string",
    "format": "email"
  }
}

// Execution Result
[{"user_email": "<EMAIL>"}]

// Enhanced Output
[{
  "data": "<EMAIL>",
  "data_type": "string",
  "property_name": "user_email",
  "semantic_type": "email"
}]
```

### Array with Semantic Types
```json
// Schema
{
  "field_name": "email_list",
  "data_type": {
    "type": "array",
    "items": {
      "type": "string",
      "format": "email"
    }
  }
}

// Execution Result
[{"email_list": ["<EMAIL>", "<EMAIL>"]}]

// Enhanced Output
[{
  "data": [
    {
      "data": "<EMAIL>",
      "data_type": "string",
      "property_name": "email_list",
      "semantic_type": "email"
    },
    {
      "data": "<EMAIL>", 
      "data_type": "string",
      "property_name": "email_list",
      "semantic_type": "email"
    }
  ],
  "data_type": "array",
  "property_name": "email_list",
  "semantic_type": "array_of_email"
}]
```

### Mixed Types
```json
// Schema
{
  "predefined_fields": [
    {
      "field_name": "user_email",
      "data_type": {"type": "string", "format": "email"}
    },
    {
      "field_name": "website_url", 
      "data_type": {"type": "string", "format": "url"}
    },
    {
      "field_name": "description",
      "data_type": {"type": "string"}
    }
  ]
}

// Enhanced Output
[
  {
    "data": "<EMAIL>",
    "data_type": "string",
    "property_name": "user_email", 
    "semantic_type": "email"
  },
  {
    "data": "https://example.com",
    "data_type": "string",
    "property_name": "website_url",
    "semantic_type": "url"
  },
  {
    "data": "User description",
    "data_type": "string", 
    "property_name": "description",
    "semantic_type": "string"
  }
]
```

## Frontend Integration Guidelines

### Rendering Based on Semantic Types

```javascript
function renderField(fieldData) {
  const { data, data_type, property_name, semantic_type } = fieldData;
  
  switch (semantic_type) {
    case 'email':
      return `<a href="mailto:${data}">${data}</a>`;
      
    case 'url':
    case 'link':
      return `<a href="${data}" target="_blank">${data}</a>`;
      
    case 'datetime':
    case 'timestamp':
      return new Date(data).toLocaleString();
      
    case 'currency':
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
      }).format(data);
      
    case 'percentage':
      return `${data}%`;
      
    case 'color':
      return `<span style="background-color: ${data}">${data}</span>`;
      
    default:
      return data;
  }
}
```

### Validation Based on Semantic Types

```javascript
function validateField(value, semantic_type) {
  switch (semantic_type) {
    case 'email':
      return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
      
    case 'url':
      try {
        new URL(value);
        return true;
      } catch {
        return false;
      }
      
    case 'percentage':
      const num = parseFloat(value);
      return !isNaN(num) && num >= 0 && num <= 100;
      
    default:
      return true;
  }
}
```

## Backward Compatibility

- All existing functionality remains intact
- Previous result format is preserved with addition of `semantic_type` field
- No breaking changes to existing APIs
- Default semantic type `"string"` ensures consistent behavior

## Testing

### Unit Tests
- `test_semantic_type_extractor.py` - Tests for semantic type extraction utility
- `test_helper_functions.py` - Tests for enhanced result formatting

### Integration Tests  
- `test_transition_handler.py` - End-to-end semantic type flow tests
- Tests cover MCP execution, Components execution, arrays, objects, and error handling

## Error Handling

- Invalid field definitions default to `"string"` semantic type
- Missing format fields default to `"string"` semantic type
- Comprehensive logging for debugging semantic type extraction
- Graceful degradation when semantic type extraction fails

## Performance Considerations

- Minimal overhead: Simple string extraction and processing
- No external dependencies or complex computations
- Efficient caching of semantic type mappings
- Optimized for high-frequency execution scenarios
