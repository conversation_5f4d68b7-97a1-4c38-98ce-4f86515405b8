"""
Integration tests for the complete universal data propagation system.

This test suite validates the entire workflow from schema conversion
through execution using the handle-based system.
"""

import pytest
import json
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from pathlib import Path

# Import the classes we're testing
from app.core_.workflow_utils import WorkflowUtils
from app.core_.transition_handler import TransitionHandler
from app.core_.state_manager import WorkflowStateManager


class TestUniversalSystemIntegration:
    """Integration test suite for the complete universal data propagation system."""

    @pytest.fixture
    def sample_workflow_data(self):
        """Load the sample workflow for integration testing."""
        workflow_path = (
            Path(__file__).parent.parent.parent.parent
            / "ruh.ai"
            / "workflow-service"
            / "testing"
            / "sample_workflow.json"
        )
        with open(workflow_path, "r") as f:
            return json.load(f)

    @pytest.fixture
    def mock_state_manager(self):
        """Create mock state manager for integration testing."""
        state_manager = Mock(spec=WorkflowStateManager)

        # Mock transition results for the sample workflow
        state_manager.get_transition_result = Mock(
            side_effect=self._mock_transition_results
        )
        return state_manager

    def _mock_transition_results(self, transition_id):
        """Mock transition results based on transition ID."""
        mock_results = {
            "transition-MCP_Candidate_Interview_candidate_suitability_pre-1747901636059": {
                "suitability_analysis": "Candidate shows strong technical skills matching the job requirements",
                "resume_details": "Senior Software Engineer with 5+ years experience in Python, React, and cloud technologies",
                "jd_details": "Full-stack developer position requiring Python, React, and AWS experience",
            },
            "transition-MCP_Candidate_Interview_generate_interview_agenda-1747901643530": {
                "interview_agenda": "1. Technical Skills Assessment\n2. System Design Discussion\n3. Behavioral Questions\n4. Company Culture Fit",
                "resume_details": "Senior Software Engineer with 5+ years experience in Python, React, and cloud technologies",
                "jd_details": "Full-stack developer position requiring Python, React, and AWS experience",
            },
            "transition-ApiRequestNode-1748247037864": {
                "result": {
                    "status": "success",
                    "data": {
                        "api_response": "External API call completed successfully",
                        "timestamp": "2024-01-01T12:00:00Z",
                    },
                }
            },
        }
        return mock_results.get(transition_id, {})

    @pytest.fixture
    def workflow_utils(self, mock_state_manager):
        """Create WorkflowUtils instance with mock state manager."""
        utils = WorkflowUtils(workflow_id="integration-test-workflow")
        utils.state_manager = mock_state_manager
        return utils

    @pytest.fixture
    def transition_handler(self, workflow_utils, mock_state_manager):
        """Create TransitionHandler instance for integration testing."""
        from unittest.mock import Mock

        # Create minimal required parameters for TransitionHandler
        transitions_by_id = {
            "test-transition": {
                "id": "test-transition",
                "transition_type": "standard",
                "sequence": 1,
                "node_info": {"node_id": "test-node"},
            }
        }
        nodes = {
            "test-node": {
                "id": "test-node",
                "server_script_path": "test/path",
                "server_tools": [],
            }
        }
        dependency_map = {}
        mock_tool_executor = Mock()

        handler = TransitionHandler(
            state_manager=mock_state_manager,
            transitions_by_id=transitions_by_id,
            nodes=nodes,
            dependency_map=dependency_map,
            workflow_utils=workflow_utils,
            tool_executor=mock_tool_executor,
        )
        return handler

    def test_handle_mapping_extraction_from_real_workflow(self, sample_workflow_data):
        """Test handle mapping extraction from real workflow data."""
        # This would test the workflow schema converter
        # For now, we'll simulate the expected handle mappings
        expected_mappings = {
            "transition-MCP_Candidate_Interview_candidate_suitability_pre-1747901636059": [
                {
                    "source_transition_id": "transition-start-node",
                    "source_handle_id": "flow",
                    "target_handle_id": "resume_s3_link",
                    "edge_id": "edge-start-to-candidate",
                }
            ],
            "transition-MCP_Candidate_Interview_generate_interview_agenda-1747901643530": [
                {
                    "source_transition_id": "transition-MCP_Candidate_Interview_candidate_suitability_pre-1747901636059",
                    "source_handle_id": "resume_details",
                    "target_handle_id": "resume_details",
                    "edge_id": "reactflow__edge-MCP_Candidate_Interview_candidate_suitability_pre-1747901636059resume_details-MCP_Candidate_Interview_generate_interview_agenda-1747901643530resume_details",
                },
                {
                    "source_transition_id": "transition-MCP_Candidate_Interview_candidate_suitability_pre-1747901636059",
                    "source_handle_id": "jd_details",
                    "target_handle_id": "jd_details",
                    "edge_id": "reactflow__edge-MCP_Candidate_Interview_candidate_suitability_pre-1747901636059jd_details-MCP_Candidate_Interview_generate_interview_agenda-1747901643530jd_details",
                },
            ],
        }

        # Verify the structure matches our expected format
        assert isinstance(expected_mappings, dict)
        for transition_id, mappings in expected_mappings.items():
            assert isinstance(mappings, list)
            for mapping in mappings:
                assert "source_transition_id" in mapping
                assert "source_handle_id" in mapping
                assert "target_handle_id" in mapping
                assert "edge_id" in mapping

    @pytest.mark.asyncio
    async def test_end_to_end_parameter_resolution(self, workflow_utils):
        """Test end-to-end parameter resolution using handle mappings."""
        # Simulate input data configs with handle mappings
        input_data_configs = [
            {
                "from_transition_id": "transition-MCP_Candidate_Interview_candidate_suitability_pre-1747901636059",
                "handle_mappings": [
                    {
                        "source_transition_id": "transition-MCP_Candidate_Interview_candidate_suitability_pre-1747901636059",
                        "source_handle_id": "resume_details",
                        "target_handle_id": "resume_details",
                        "edge_id": "test-edge-1",
                    },
                    {
                        "source_transition_id": "transition-MCP_Candidate_Interview_candidate_suitability_pre-1747901636059",
                        "source_handle_id": "jd_details",
                        "target_handle_id": "jd_details",
                        "edge_id": "test-edge-2",
                    },
                ],
            }
        ]

        current_tool_params = {"prompt": "Generate a comprehensive interview agenda"}

        # Test parameter resolution
        result = await workflow_utils._format_tool_parameters(
            {},
            input_data_configs,
            "transition-MCP_Candidate_Interview_generate_interview_agenda-1747901643530",
            current_tool_params,
        )

        # Verify handle-based resolution worked
        assert "resume_details" in result
        assert "jd_details" in result
        assert "prompt" in result
        assert (
            result["resume_details"]
            == "Senior Software Engineer with 5+ years experience in Python, React, and cloud technologies"
        )
        assert (
            result["jd_details"]
            == "Full-stack developer position requiring Python, React, and AWS experience"
        )
        assert result["prompt"] == "Generate a comprehensive interview agenda"

    @pytest.mark.asyncio
    async def test_universal_parameter_resolution_with_result_resolution(
        self, transition_handler
    ):
        """Test universal parameter resolution using result_resolution metadata."""
        # Create a transition with result_resolution metadata
        transition = {
            "id": "test-transition",
            "result_resolution": {
                "node_type": "mcp",
                "expected_result_structure": "nested_result",
                "handle_registry": {
                    "output_handles": [
                        {"handle_id": "interview_agenda", "data_type": "string"},
                        {"handle_id": "resume_details", "data_type": "string"},
                    ]
                },
                "dynamic_discovery": {
                    "enabled": True,
                    "fallback_patterns": [
                        "result.{handle_id}",
                        "{handle_id}",
                        "output_data.{handle_id}",
                    ],
                },
            },
        }

        input_data_configs = [
            {
                "from_transition_id": "transition-MCP_Candidate_Interview_candidate_suitability_pre-1747901636059",
                "handle_mappings": [
                    {
                        "source_transition_id": "transition-MCP_Candidate_Interview_candidate_suitability_pre-1747901636059",
                        "source_handle_id": "resume_details",
                        "target_handle_id": "resume_details",
                        "edge_id": "test-edge",
                    }
                ],
            }
        ]

        tool_params_config = {"static_param": "static_value"}

        # Test enhanced parameter resolution
        result = await transition_handler._resolve_tool_parameters_universally(
            {}, input_data_configs, "test-transition", tool_params_config, transition
        )

        # Verify enhanced resolution worked
        assert "resume_details" in result
        assert "static_param" in result
        assert (
            result["resume_details"]
            == "Senior Software Engineer with 5+ years experience in Python, React, and cloud technologies"
        )
        assert result["static_param"] == "static_value"

    def test_dynamic_result_structure_analysis_integration(self, workflow_utils):
        """Test dynamic result structure analysis with real-world data structures."""
        # Test with MCP-style result
        mcp_result = {
            "suitability_analysis": "Strong candidate",
            "resume_details": "Detailed resume content",
            "jd_details": "Job description content",
        }

        analysis = workflow_utils.create_dynamic_result_structure_analyzer(
            mcp_result, "mcp"
        )

        assert analysis["structure_pattern"] == "dictionary"
        assert "suitability_analysis" in analysis["handle_candidates"]
        assert len(analysis["handle_candidates"]) == 3

        # Test with API-style result
        api_result = {
            "result": {
                "status": "success",
                "data": {
                    "api_response": "Response data",
                    "metadata": {"timestamp": "2024-01-01"},
                },
            }
        }

        analysis = workflow_utils.create_dynamic_result_structure_analyzer(
            api_result, "api"
        )

        assert analysis["structure_pattern"] == "dictionary"
        assert "result.data.api_response" in analysis["available_paths"]
        assert any("'result' field found" in rec for rec in analysis["recommendations"])

    def test_comprehensive_workflow_validation_integration(
        self, workflow_utils, sample_workflow_data
    ):
        """Test comprehensive workflow validation with real workflow data."""
        # Create mock transitions data based on sample workflow
        transitions_data = [
            {
                "id": "transition-1",
                "node_info": {
                    "input_data": [
                        {
                            "handle_mappings": [
                                {
                                    "source_handle_id": "resume_details",
                                    "target_handle_id": "resume_details",
                                    "edge_id": "edge-1",
                                }
                            ]
                        }
                    ]
                },
            }
        ]

        # Test comprehensive validation
        validation_result = workflow_utils.create_comprehensive_handle_validator(
            sample_workflow_data, transitions_data
        )

        # Verify validation results
        assert "workflow_validation" in validation_result
        assert "node_validation" in validation_result
        assert "connection_validation" in validation_result
        assert "recommendations" in validation_result

        # Check that all nodes were validated
        nodes_count = len(sample_workflow_data["nodes"])
        assert len(validation_result["node_validation"]) == nodes_count

        # Verify workflow validation summary
        workflow_validation = validation_result["workflow_validation"]
        assert workflow_validation["total_handle_connections"] >= 0
        assert workflow_validation["overall_status"] in [
            "valid",
            "mostly_valid",
            "invalid",
            "no_connections",
        ]

    def test_handle_debugging_tools_integration(
        self, workflow_utils, sample_workflow_data
    ):
        """Test handle debugging tools with real workflow data."""
        # Create mock transitions data
        transitions_data = [
            {
                "id": "transition-1",
                "node_info": {
                    "input_data": [
                        {
                            "handle_mappings": [
                                {
                                    "source_handle_id": "output1",
                                    "target_handle_id": "input1",
                                    "edge_id": "edge-1",
                                }
                            ]
                        }
                    ]
                },
            }
        ]

        # Test debugging tools creation
        debugging_tools = workflow_utils.create_handle_debugging_tools(
            sample_workflow_data, transitions_data
        )

        # Verify debugging tools structure
        assert "handle_map" in debugging_tools
        assert "connection_graph" in debugging_tools
        assert "execution_analysis" in debugging_tools
        assert "troubleshooting_guide" in debugging_tools

        # Verify troubleshooting guide content
        guide = debugging_tools["troubleshooting_guide"]
        assert len(guide) > 0
        assert all("issue" in item and "solutions" in item for item in guide)

    @pytest.mark.asyncio
    async def test_fallback_to_placeholder_system(self, workflow_utils):
        """Test fallback to placeholder system when handle mappings are not available."""
        # Test with no handle mappings (should fall back to placeholder system)
        input_data_configs = [
            {
                "from_transition_id": "transition-source",
                # No handle_mappings - should trigger fallback
            }
        ]

        current_tool_params = {
            "field_with_placeholder": "${some_field}",
            "static_field": "static_value",
        }

        # Mock the state manager to return results for placeholder resolution
        workflow_utils.state_manager.get_transition_result.return_value = {
            "some_field": "resolved_value"
        }

        result = await workflow_utils._format_tool_parameters(
            {}, input_data_configs, "test-transition", current_tool_params
        )

        # Verify fallback worked
        assert "static_field" in result
        assert result["static_field"] == "static_value"
        # The placeholder resolution would depend on the existing implementation


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
