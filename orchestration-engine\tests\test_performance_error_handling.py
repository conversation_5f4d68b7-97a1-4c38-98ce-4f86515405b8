"""
Test performance and error handling capabilities of the universal data propagation system.

This test suite validates performance optimizations, error handling, and logging
capabilities of the handle-based system.
"""

import pytest
import time
from unittest.mock import Mock, patch

# Import the classes we're testing
from app.core_.workflow_utils import WorkflowUtils
from app.core_.state_manager import WorkflowStateManager


class TestPerformanceOptimizations:
    """Test suite for performance optimization features."""

    @pytest.fixture
    def workflow_utils(self):
        """Create WorkflowUtils instance for testing."""
        return WorkflowUtils(workflow_id="performance-test-workflow")

    def test_performance_optimized_resolver_creation(self, workflow_utils):
        """Test creation of performance-optimized handle resolver."""
        workflow_data = {
            "nodes": [
                {"id": f"node-{i}", "type": "test"} for i in range(25)
            ]  # 25 nodes for optimization triggers
        }

        transitions_data = [
            {
                "id": f"transition-{i}",
                "node_info": {
                    "input_data": [
                        {
                            "handle_mappings": [
                                {
                                    "source_transition_id": f"transition-{i-1}",
                                    "source_handle_id": f"output_{i-1}",
                                    "target_handle_id": f"input_{i}",
                                    "edge_id": f"edge-{i}",
                                }
                            ]
                        }
                    ]
                },
            }
            for i in range(1, 25)
        ]

        resolver = workflow_utils.create_performance_optimized_handle_resolver(
            workflow_data, transitions_data
        )

        # Verify resolver structure
        assert "handle_lookup_cache" in resolver
        assert "connection_graph" in resolver
        assert "performance_metrics" in resolver
        assert "optimization_strategies" in resolver

        # Verify cache was populated
        assert len(resolver["handle_lookup_cache"]) > 0

        # Verify optimization strategies were identified
        assert len(resolver["optimization_strategies"]) > 0

        # Check for batch processing strategy (triggered by >20 nodes)
        strategy_names = [s["strategy"] for s in resolver["optimization_strategies"]]
        assert "batch_processing" in strategy_names

    def test_optimization_opportunities_analysis(self, workflow_utils):
        """Test analysis of optimization opportunities."""
        # Test with large workflow (should suggest batch processing)
        connection_graph = {f"node-{i}": [f"node-{i+1}"] for i in range(30)}
        strategies = workflow_utils._analyze_optimization_opportunities(
            connection_graph, 30
        )

        strategy_names = [s["strategy"] for s in strategies]
        assert "batch_processing" in strategy_names

        # Test with highly connected workflow (should suggest parallel execution)
        connection_graph = {"central_node": [f"node-{i}" for i in range(10)]}
        strategies = workflow_utils._analyze_optimization_opportunities(
            connection_graph, 15
        )

        strategy_names = [s["strategy"] for s in strategies]
        assert "parallel_execution" in strategy_names

    def test_performance_benchmarking(self, workflow_utils):
        """Test performance benchmarking system."""
        # Create test scenarios with varying complexity
        test_scenarios = [
            {
                "name": "simple_scenario",
                "complexity": "low",
                "handle_mappings": {
                    "source-1": [
                        {
                            "source_handle_id": "output1",
                            "target_handle_id": "input1",
                            "edge_id": "edge-1",
                        }
                    ]
                },
                "previous_results": {"source-1": {"output1": "test_value"}},
            },
            {
                "name": "complex_scenario",
                "complexity": "high",
                "handle_mappings": {
                    f"source-{i}": [
                        {
                            "source_handle_id": f"output{i}",
                            "target_handle_id": f"input{i}",
                            "edge_id": f"edge-{i}",
                        }
                    ]
                    for i in range(10)
                },
                "previous_results": {
                    f"source-{i}": {f"output{i}": f"value_{i}"} for i in range(10)
                },
            },
        ]

        benchmark_results = workflow_utils.benchmark_handle_resolution_performance(
            test_scenarios
        )

        # Verify benchmark structure
        assert "scenarios" in benchmark_results
        assert "summary" in benchmark_results

        # Verify scenarios were benchmarked
        assert "simple_scenario" in benchmark_results["scenarios"]
        assert "complex_scenario" in benchmark_results["scenarios"]

        # Verify summary statistics
        summary = benchmark_results["summary"]
        assert "average_resolution_time" in summary
        assert "performance_grade" in summary
        assert summary["total_scenarios"] == 2


class TestErrorHandling:
    """Test suite for error handling capabilities."""

    @pytest.fixture
    def workflow_utils(self):
        """Create WorkflowUtils instance for testing."""
        return WorkflowUtils(workflow_id="error-test-workflow")

    def test_comprehensive_error_handler_creation(self, workflow_utils):
        """Test creation of comprehensive error handler."""
        error_handler = workflow_utils.create_comprehensive_error_handler()

        # Verify error handler structure
        assert "error_categories" in error_handler
        assert "recovery_strategies" in error_handler
        assert "error_metrics" in error_handler

        # Verify error categories
        categories = error_handler["error_categories"]
        assert "MALFORMED_MAPPING" in categories
        assert "MISSING_SOURCE_DATA" in categories
        assert "TYPE_MISMATCH" in categories
        assert "CIRCULAR_DEPENDENCY" in categories
        assert "TIMEOUT" in categories

        # Verify each category has required fields
        for category, config in categories.items():
            assert "severity" in config
            assert "recovery_strategy" in config
            assert "user_message" in config

    def test_skip_malformed_mapping_recovery(self, workflow_utils):
        """Test recovery strategy for malformed mappings."""
        malformed_mapping = {"invalid": "mapping"}
        error_context = {"error": "Missing required fields"}

        result = workflow_utils._skip_malformed_mapping(
            malformed_mapping, error_context
        )

        # Should return None for malformed mappings
        assert result is None

    def test_use_default_value_recovery(self, workflow_utils):
        """Test recovery strategy for missing source data."""
        mapping = {"source_handle_id": "missing_field"}
        error_context = {"default_value": "fallback_value"}

        result = workflow_utils._use_default_value(mapping, error_context)

        # Should return the default value
        assert result == "fallback_value"

    def test_type_conversion_recovery(self, workflow_utils):
        """Test recovery strategy for type mismatches."""
        # Test successful conversions
        assert workflow_utils._attempt_type_conversion("123", "number", {}) == 123
        assert workflow_utils._attempt_type_conversion("123.45", "number", {}) == 123.45
        assert workflow_utils._attempt_type_conversion(123, "string", {}) == "123"
        assert workflow_utils._attempt_type_conversion("true", "boolean", {}) == True
        assert workflow_utils._attempt_type_conversion("test", "object", {}) == {
            "value": "test"
        }

        # Test failed conversion (should return original value)
        result = workflow_utils._attempt_type_conversion("invalid", "number", {})
        assert result == "invalid"  # Should return original value on failure

    def test_circular_dependency_recovery(self, workflow_utils):
        """Test recovery strategy for circular dependencies."""
        cycle_info = {"weakest_link": "edge-1"}
        error_context = {}

        result = workflow_utils._break_circular_dependency(cycle_info, error_context)

        # Should return cycle breaking information
        assert result["cycle_broken"] == True
        assert result["removed_connection"] == "edge-1"

    def test_timeout_recovery(self, workflow_utils):
        """Test recovery strategy for timeouts."""
        timeout_info = {"timeout_duration": "30s"}
        error_context = {}

        # Should raise TimeoutError
        with pytest.raises(TimeoutError):
            workflow_utils._abort_resolution(timeout_info, error_context)

    @pytest.mark.asyncio
    async def test_error_handling_in_parameter_resolution(self, workflow_utils):
        """Test error handling during parameter resolution."""
        # Mock state manager to return None (missing data)
        mock_state_manager = Mock()
        mock_state_manager.get_transition_result.return_value = None
        workflow_utils.state_manager = mock_state_manager

        input_data_configs = [
            {
                "from_transition_id": "missing-transition",
                "handle_mappings": [
                    {
                        "source_handle_id": "missing_field",
                        "target_handle_id": "target_field",
                        "edge_id": "test-edge",
                    }
                ],
            }
        ]

        current_tool_params = {"static_param": "static_value"}

        # Should handle missing data gracefully
        result = await workflow_utils._format_tool_parameters(
            {}, input_data_configs, "test-transition", current_tool_params
        )

        # Should still return static parameters
        assert "static_param" in result
        assert result["static_param"] == "static_value"


class TestEnhancedLogging:
    """Test suite for enhanced logging capabilities."""

    @pytest.fixture
    def workflow_utils(self):
        """Create WorkflowUtils instance for testing."""
        return WorkflowUtils(workflow_id="logging-test-workflow")

    def test_enhanced_logging_system_creation(self, workflow_utils):
        """Test creation of enhanced logging system."""
        logging_system = workflow_utils.create_enhanced_logging_system()

        # Verify logging system structure
        assert "loggers" in logging_system
        assert "log_levels" in logging_system
        assert "formatters" in logging_system
        assert "metrics" in logging_system

        # Verify loggers
        loggers = logging_system["loggers"]
        assert "handle_resolution" in loggers
        assert "performance" in loggers
        assert "validation" in loggers
        assert "debugging" in loggers

        # Verify formatters
        formatters = logging_system["formatters"]
        assert "detailed" in formatters
        assert "performance" in formatters
        assert "simple" in formatters

    def test_logging_configuration(self, workflow_utils):
        """Test logging configuration and setup."""
        with patch("logging.getLogger") as mock_get_logger:
            mock_logger = Mock()
            mock_get_logger.return_value = mock_logger

            logging_system = workflow_utils.create_enhanced_logging_system()

            # Verify loggers were configured
            assert mock_get_logger.call_count >= 4  # At least 4 loggers

            # Verify logger setup was called
            assert mock_logger.setLevel.called
            assert mock_logger.addHandler.called


class TestTimeoutHandling:
    """Test suite for timeout handling capabilities."""

    @pytest.fixture
    def workflow_utils(self):
        """Create WorkflowUtils instance for testing."""
        return WorkflowUtils(workflow_id="timeout-test-workflow")

    @pytest.mark.asyncio
    async def test_timeout_in_complex_resolution(self, workflow_utils):
        """Test timeout handling in complex handle resolution scenarios."""
        # Create a scenario that could potentially timeout
        complex_handle_mappings = {
            f"source-{i}": [
                {
                    "source_handle_id": f"output_{j}",
                    "target_handle_id": f"input_{j}",
                    "edge_id": f"edge-{i}-{j}",
                }
                for j in range(100)  # Many mappings per source
            ]
            for i in range(50)  # Many sources
        }

        # Mock results for all sources
        all_previous_results = {
            f"source-{i}": {f"output_{j}": f"value_{i}_{j}" for j in range(100)}
            for i in range(50)
        }

        # This should complete without timeout for our test
        start_time = time.time()
        result = workflow_utils.create_universal_parameter_mapping(
            complex_handle_mappings, all_previous_results
        )
        execution_time = time.time() - start_time

        # Verify it completed and didn't timeout
        assert "resolved_parameters" in result
        assert "mapping_metadata" in result
        assert execution_time < 10.0  # Should complete within 10 seconds


class TestOneToOneConstraintValidation:
    """Test suite for one-to-one constraint validation in orchestration engine."""

    @pytest.fixture
    def workflow_utils(self):
        """Create WorkflowUtils instance for testing."""
        return WorkflowUtils(workflow_id="one-to-one-test-workflow")

    def test_one_to_one_constraint_validation_with_valid_workflow(self, workflow_utils):
        """Test one-to-one constraint validation with valid workflow."""
        workflow_data = {
            "nodes": [
                {"id": "node-1", "type": "test"},
                {"id": "node-2", "type": "test"},
                {"id": "node-3", "type": "test"},
            ],
            "edges": [
                {
                    "id": "edge-1",
                    "source": "node-1",
                    "target": "node-2",
                    "sourceHandle": "output1",
                    "targetHandle": "input1",
                },
                {
                    "id": "edge-2",
                    "source": "node-2",
                    "target": "node-3",
                    "sourceHandle": "output2",
                    "targetHandle": "input2",
                },
            ],
        }

        transitions_data = []

        validation_result = workflow_utils.create_comprehensive_handle_validator(
            workflow_data, transitions_data
        )

        # Should have no one-to-one violations
        assert (
            validation_result["workflow_validation"]["one_to_one_constraint_status"]
            == "valid"
        )
        assert validation_result["workflow_validation"]["one_to_one_violations"] == 0
        assert len(validation_result["one_to_one_violations"]) == 0

    def test_one_to_one_constraint_validation_with_violations(self, workflow_utils):
        """Test one-to-one constraint validation with violations."""
        workflow_data = {
            "nodes": [
                {"id": "node-1", "type": "test"},
                {"id": "node-2", "type": "test"},
                {"id": "node-3", "type": "test"},
            ],
            "edges": [
                {
                    "id": "edge-1",
                    "source": "node-1",
                    "target": "node-3",
                    "sourceHandle": "output1",
                    "targetHandle": "input1",  # Same target handle
                },
                {
                    "id": "edge-2",
                    "source": "node-2",
                    "target": "node-3",
                    "sourceHandle": "output2",
                    "targetHandle": "input1",  # Same target handle - VIOLATION!
                },
            ],
        }

        transitions_data = []

        validation_result = workflow_utils.create_comprehensive_handle_validator(
            workflow_data, transitions_data
        )

        # Should detect one-to-one violations
        assert (
            validation_result["workflow_validation"]["one_to_one_constraint_status"]
            == "violated"
        )
        assert validation_result["workflow_validation"]["one_to_one_violations"] == 1
        assert len(validation_result["one_to_one_violations"]) == 1

        violation = validation_result["one_to_one_violations"][0]
        assert violation["type"] == "one_to_one_constraint_violation"
        assert violation["target_node"] == "node-3"
        assert violation["target_handle"] == "input1"
        assert violation["violation_count"] == 2
        assert violation["severity"] == "critical"
        assert len(violation["sources"]) == 2

    def test_one_to_one_constraint_recommendations(self, workflow_utils):
        """Test that one-to-one constraint violations generate appropriate recommendations."""
        workflow_data = {
            "nodes": [
                {"id": "source-1", "type": "test"},
                {"id": "source-2", "type": "test"},
                {"id": "source-3", "type": "test"},
                {"id": "target", "type": "test"},
            ],
            "edges": [
                {
                    "id": "edge-1",
                    "source": "source-1",
                    "target": "target",
                    "sourceHandle": "result1",
                    "targetHandle": "data_input",
                },
                {
                    "id": "edge-2",
                    "source": "source-2",
                    "target": "target",
                    "sourceHandle": "result2",
                    "targetHandle": "data_input",
                },
                {
                    "id": "edge-3",
                    "source": "source-3",
                    "target": "target",
                    "sourceHandle": "result3",
                    "targetHandle": "data_input",
                },
            ],
        }

        transitions_data = []

        validation_result = workflow_utils.create_comprehensive_handle_validator(
            workflow_data, transitions_data
        )

        # Should generate critical recommendations
        recommendations = validation_result["recommendations"]
        assert any("CRITICAL" in rec for rec in recommendations)
        assert any("one-to-one constraint violations" in rec for rec in recommendations)
        assert any("target.data_input has 3 sources" in rec for rec in recommendations)

    def test_one_to_one_constraint_with_flow_connections_ignored(self, workflow_utils):
        """Test that flow connections without handles are ignored."""
        workflow_data = {
            "nodes": [
                {"id": "node-1", "type": "test"},
                {"id": "node-2", "type": "test"},
                {"id": "node-3", "type": "test"},
            ],
            "edges": [
                # Valid handle connection
                {
                    "id": "edge-1",
                    "source": "node-1",
                    "target": "node-2",
                    "sourceHandle": "output1",
                    "targetHandle": "input1",
                },
                # Flow connection without handles - should be ignored
                {
                    "id": "edge-2",
                    "source": "node-2",
                    "target": "node-3",
                    # No sourceHandle/targetHandle
                },
            ],
        }

        transitions_data = []

        validation_result = workflow_utils.create_comprehensive_handle_validator(
            workflow_data, transitions_data
        )

        # Should be valid since flow connections are ignored
        assert (
            validation_result["workflow_validation"]["one_to_one_constraint_status"]
            == "valid"
        )
        assert validation_result["workflow_validation"]["one_to_one_violations"] == 0

    def test_one_to_one_constraint_detailed_violation_info(self, workflow_utils):
        """Test detailed violation information structure."""
        workflow_data = {
            "nodes": [
                {"id": "producer-a", "type": "test"},
                {"id": "producer-b", "type": "test"},
                {"id": "consumer", "type": "test"},
            ],
            "edges": [
                {
                    "id": "connection-alpha",
                    "source": "producer-a",
                    "target": "consumer",
                    "sourceHandle": "alpha_output",
                    "targetHandle": "shared_input",
                },
                {
                    "id": "connection-beta",
                    "source": "producer-b",
                    "target": "consumer",
                    "sourceHandle": "beta_output",
                    "targetHandle": "shared_input",
                },
            ],
        }

        transitions_data = []

        validation_result = workflow_utils.create_comprehensive_handle_validator(
            workflow_data, transitions_data
        )

        violation = validation_result["one_to_one_violations"][0]

        # Verify detailed violation structure
        assert violation["target_identifier"] == "consumer.shared_input"
        assert violation["violation_count"] == 2
        assert violation["severity"] == "critical"

        # Verify source details
        sources = violation["sources"]
        assert len(sources) == 2

        source_identifiers = [s["source_identifier"] for s in sources]
        assert "producer-a.alpha_output" in source_identifiers
        assert "producer-b.beta_output" in source_identifiers

        edge_ids = [s["edge_id"] for s in sources]
        assert "connection-alpha" in edge_ids
        assert "connection-beta" in edge_ids


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
