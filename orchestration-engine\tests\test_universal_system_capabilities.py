"""
Test universal system capabilities with different node types and workflow complexities.

This test suite validates that the handle-based system works universally
with any node type combination and workflow complexity.
"""

import pytest
import json
import asyncio
from unittest.mock import Mock, AsyncMock, patch

# Import the classes we're testing
from app.core_.workflow_utils import WorkflowUtils
from app.core_.transition_handler import <PERSON>ionHand<PERSON>
from app.core_.state_manager import WorkflowStateManager


class TestUniversalSystemCapabilities:
    """Test suite for universal system capabilities across different scenarios."""

    @pytest.fixture
    def workflow_utils(self):
        """Create WorkflowUtils instance for testing."""
        return WorkflowUtils(workflow_id="universal-test-workflow")

    @pytest.fixture
    def mock_state_manager(self):
        """Create mock state manager."""
        state_manager = Mock(spec=WorkflowStateManager)
        state_manager.get_transition_result = Mock()
        return state_manager

    def test_mcp_only_workflow(self, workflow_utils):
        """Test workflow containing only MCP nodes."""
        mcp_workflow_data = {
            "nodes": [
                {
                    "id": "mcp-node-1",
                    "data": {
                        "type": "mcp",
                        "definition": {
                            "inputs": [{"name": "input1", "data_type": "string"}],
                            "outputs": [{"name": "output1", "data_type": "string"}]
                        }
                    }
                },
                {
                    "id": "mcp-node-2",
                    "data": {
                        "type": "mcp",
                        "definition": {
                            "inputs": [{"name": "input2", "data_type": "string"}],
                            "outputs": [{"name": "output2", "data_type": "string"}]
                        }
                    }
                }
            ]
        }
        
        transitions_data = [
            {
                "id": "transition-mcp-1",
                "node_info": {
                    "input_data": [
                        {
                            "handle_mappings": [
                                {
                                    "source_handle_id": "output1",
                                    "target_handle_id": "input2",
                                    "edge_id": "mcp-edge-1"
                                }
                            ]
                        }
                    ]
                }
            }
        ]
        
        # Test validation with MCP-only workflow
        validation_result = workflow_utils.create_comprehensive_handle_validator(
            mcp_workflow_data, transitions_data
        )
        
        assert validation_result["workflow_validation"]["overall_status"] in ["valid", "mostly_valid"]
        assert len(validation_result["node_validation"]) == 2
        
        # Verify all nodes are recognized as MCP type
        for node_id, validation in validation_result["node_validation"].items():
            assert validation["node_type"] == "mcp"

    def test_component_only_workflow(self, workflow_utils):
        """Test workflow containing only Component nodes."""
        component_workflow_data = {
            "nodes": [
                {
                    "id": "component-node-1",
                    "data": {
                        "type": "component",
                        "definition": {
                            "inputs": [{"name": "text_input", "data_type": "string"}],
                            "outputs": [{"name": "processed_text", "data_type": "string"}]
                        }
                    }
                },
                {
                    "id": "component-node-2",
                    "data": {
                        "type": "component",
                        "definition": {
                            "inputs": [{"name": "data_input", "data_type": "object"}],
                            "outputs": [{"name": "formatted_data", "data_type": "object"}]
                        }
                    }
                }
            ]
        }
        
        transitions_data = [
            {
                "id": "transition-component-1",
                "node_info": {
                    "input_data": [
                        {
                            "handle_mappings": [
                                {
                                    "source_handle_id": "processed_text",
                                    "target_handle_id": "data_input",
                                    "edge_id": "component-edge-1"
                                }
                            ]
                        }
                    ]
                }
            }
        ]
        
        # Test validation with Component-only workflow
        validation_result = workflow_utils.create_comprehensive_handle_validator(
            component_workflow_data, transitions_data
        )
        
        assert validation_result["workflow_validation"]["overall_status"] in ["valid", "mostly_valid"]
        assert len(validation_result["node_validation"]) == 2
        
        # Verify all nodes are recognized as component type
        for node_id, validation in validation_result["node_validation"].items():
            assert validation["node_type"] == "component"

    def test_mixed_node_type_workflow(self, workflow_utils):
        """Test workflow with mixed node types (MCP + Component + API + Custom)."""
        mixed_workflow_data = {
            "nodes": [
                {
                    "id": "mcp-node",
                    "data": {
                        "type": "mcp",
                        "definition": {
                            "inputs": [{"name": "mcp_input", "data_type": "string"}],
                            "outputs": [{"name": "mcp_output", "data_type": "string"}]
                        }
                    }
                },
                {
                    "id": "component-node",
                    "data": {
                        "type": "component",
                        "definition": {
                            "inputs": [{"name": "comp_input", "data_type": "string"}],
                            "outputs": [{"name": "comp_output", "data_type": "object"}]
                        }
                    }
                },
                {
                    "id": "api-node",
                    "data": {
                        "type": "api",
                        "definition": {
                            "inputs": [{"name": "api_input", "data_type": "object"}],
                            "outputs": [{"name": "api_response", "data_type": "object"}]
                        }
                    }
                },
                {
                    "id": "custom-node",
                    "data": {
                        "type": "custom",
                        "definition": {
                            "inputs": [{"name": "custom_input", "data_type": "object"}],
                            "outputs": [{"name": "custom_output", "data_type": "string"}]
                        }
                    }
                }
            ]
        }
        
        transitions_data = [
            {
                "id": "transition-mixed-1",
                "node_info": {
                    "input_data": [
                        {
                            "handle_mappings": [
                                {
                                    "source_handle_id": "mcp_output",
                                    "target_handle_id": "comp_input",
                                    "edge_id": "mixed-edge-1"
                                }
                            ]
                        }
                    ]
                }
            },
            {
                "id": "transition-mixed-2",
                "node_info": {
                    "input_data": [
                        {
                            "handle_mappings": [
                                {
                                    "source_handle_id": "comp_output",
                                    "target_handle_id": "api_input",
                                    "edge_id": "mixed-edge-2"
                                }
                            ]
                        }
                    ]
                }
            },
            {
                "id": "transition-mixed-3",
                "node_info": {
                    "input_data": [
                        {
                            "handle_mappings": [
                                {
                                    "source_handle_id": "api_response",
                                    "target_handle_id": "custom_input",
                                    "edge_id": "mixed-edge-3"
                                }
                            ]
                        }
                    ]
                }
            }
        ]
        
        # Test validation with mixed node types
        validation_result = workflow_utils.create_comprehensive_handle_validator(
            mixed_workflow_data, transitions_data
        )
        
        assert validation_result["workflow_validation"]["overall_status"] in ["valid", "mostly_valid"]
        assert len(validation_result["node_validation"]) == 4
        
        # Verify all different node types are recognized
        node_types = [validation["node_type"] for validation in validation_result["node_validation"].values()]
        assert "mcp" in node_types
        assert "component" in node_types
        assert "api" in node_types
        assert "custom" in node_types

    def test_unknown_future_node_types(self, workflow_utils):
        """Test system works with unknown/future node types."""
        future_workflow_data = {
            "nodes": [
                {
                    "id": "future-node-1",
                    "data": {
                        "type": "quantum_processor",  # Unknown future node type
                        "definition": {
                            "inputs": [{"name": "quantum_input", "data_type": "quantum_state"}],
                            "outputs": [{"name": "quantum_output", "data_type": "quantum_result"}]
                        }
                    }
                },
                {
                    "id": "future-node-2",
                    "data": {
                        "type": "ai_agent",  # Another unknown future node type
                        "definition": {
                            "inputs": [{"name": "agent_input", "data_type": "instruction"}],
                            "outputs": [{"name": "agent_response", "data_type": "action"}]
                        }
                    }
                }
            ]
        }
        
        transitions_data = [
            {
                "id": "transition-future-1",
                "node_info": {
                    "input_data": [
                        {
                            "handle_mappings": [
                                {
                                    "source_handle_id": "quantum_output",
                                    "target_handle_id": "agent_input",
                                    "edge_id": "future-edge-1"
                                }
                            ]
                        }
                    ]
                }
            }
        ]
        
        # Test that unknown node types are handled gracefully
        validation_result = workflow_utils.create_comprehensive_handle_validator(
            future_workflow_data, transitions_data
        )
        
        # System should handle unknown types without errors
        assert "workflow_validation" in validation_result
        assert len(validation_result["node_validation"]) == 2
        
        # Verify unknown types are captured
        node_types = [validation["node_type"] for validation in validation_result["node_validation"].values()]
        assert "quantum_processor" in node_types
        assert "ai_agent" in node_types

    @pytest.mark.asyncio
    async def test_parameter_resolution_with_unknown_node_types(self, workflow_utils, mock_state_manager):
        """Test parameter resolution works with unknown node types."""
        workflow_utils.state_manager = mock_state_manager
        
        # Mock results from unknown node type
        mock_state_manager.get_transition_result.return_value = {
            "quantum_output": "quantum_processed_data",
            "metadata": {"processing_time": "0.001ms"}
        }
        
        input_data_configs = [
            {
                "from_transition_id": "transition-quantum-processor",
                "handle_mappings": [
                    {
                        "source_handle_id": "quantum_output",
                        "target_handle_id": "agent_input",
                        "edge_id": "quantum-to-agent"
                    }
                ]
            }
        ]
        
        current_tool_params = {"mode": "autonomous"}
        
        # Test parameter resolution with unknown node type
        result = await workflow_utils._format_tool_parameters(
            {},
            input_data_configs,
            "transition-ai-agent",
            current_tool_params
        )
        
        # Should work regardless of node type
        assert "agent_input" in result
        assert "mode" in result
        assert result["agent_input"] == "quantum_processed_data"
        assert result["mode"] == "autonomous"

    def test_simple_workflow_complexity(self, workflow_utils):
        """Test with simple 2-node workflow."""
        simple_workflow = {
            "nodes": [
                {
                    "id": "node-1",
                    "data": {
                        "type": "start",
                        "definition": {
                            "inputs": [],
                            "outputs": [{"name": "start_output", "data_type": "string"}]
                        }
                    }
                },
                {
                    "id": "node-2",
                    "data": {
                        "type": "end",
                        "definition": {
                            "inputs": [{"name": "end_input", "data_type": "string"}],
                            "outputs": []
                        }
                    }
                }
            ]
        }
        
        transitions_data = [
            {
                "id": "simple-transition",
                "node_info": {
                    "input_data": [
                        {
                            "handle_mappings": [
                                {
                                    "source_handle_id": "start_output",
                                    "target_handle_id": "end_input",
                                    "edge_id": "simple-edge"
                                }
                            ]
                        }
                    ]
                }
            }
        ]
        
        validation_result = workflow_utils.create_comprehensive_handle_validator(
            simple_workflow, transitions_data
        )
        
        assert validation_result["workflow_validation"]["overall_status"] in ["valid", "mostly_valid"]
        assert validation_result["workflow_validation"]["total_handle_connections"] == 1

    def test_complex_workflow_50_plus_nodes(self, workflow_utils):
        """Test with complex workflow (50+ nodes)."""
        # Generate a complex workflow with 50+ nodes
        complex_workflow = {"nodes": []}
        transitions_data = []
        
        # Create 55 nodes with various types
        node_types = ["mcp", "component", "api", "custom", "processor"]
        for i in range(55):
            node_type = node_types[i % len(node_types)]
            node = {
                "id": f"node-{i}",
                "data": {
                    "type": node_type,
                    "definition": {
                        "inputs": [{"name": f"input_{i}", "data_type": "string"}] if i > 0 else [],
                        "outputs": [{"name": f"output_{i}", "data_type": "string"}] if i < 54 else []
                    }
                }
            }
            complex_workflow["nodes"].append(node)
            
            # Create transitions connecting nodes
            if i > 0:
                transition = {
                    "id": f"transition-{i}",
                    "node_info": {
                        "input_data": [
                            {
                                "handle_mappings": [
                                    {
                                        "source_handle_id": f"output_{i-1}",
                                        "target_handle_id": f"input_{i}",
                                        "edge_id": f"edge-{i-1}-to-{i}"
                                    }
                                ]
                            }
                        ]
                    }
                }
                transitions_data.append(transition)
        
        # Test validation with complex workflow
        validation_result = workflow_utils.create_comprehensive_handle_validator(
            complex_workflow, transitions_data
        )
        
        assert len(validation_result["node_validation"]) == 55
        assert validation_result["workflow_validation"]["total_handle_connections"] == 54
        assert validation_result["workflow_validation"]["overall_status"] in ["valid", "mostly_valid", "partially_compatible"]

    def test_dynamic_result_structure_with_various_node_types(self, workflow_utils):
        """Test dynamic result structure analysis with various node types."""
        # Test MCP-style result
        mcp_result = {
            "primary_output": "MCP processed result",
            "secondary_data": {"metadata": "additional info"}
        }
        
        analysis = workflow_utils.create_dynamic_result_structure_analyzer(mcp_result, "mcp")
        assert analysis["node_type"] == "mcp"
        assert "primary_output" in analysis["handle_candidates"]
        
        # Test Component-style result
        component_result = {
            "output_data": {
                "processed_content": "Component result",
                "statistics": {"count": 42}
            }
        }
        
        analysis = workflow_utils.create_dynamic_result_structure_analyzer(component_result, "component")
        assert analysis["node_type"] == "component"
        assert "output_data.processed_content" in analysis["available_paths"]
        
        # Test API-style result
        api_result = {
            "result": {
                "status": "success",
                "data": "API response data"
            }
        }
        
        analysis = workflow_utils.create_dynamic_result_structure_analyzer(api_result, "api")
        assert analysis["node_type"] == "api"
        assert any("'result' field found" in rec for rec in analysis["recommendations"])
        
        # Test unknown node type result
        unknown_result = {
            "quantum_state": "superposition",
            "entanglement_data": [1, 0, 1, 0]
        }
        
        analysis = workflow_utils.create_dynamic_result_structure_analyzer(unknown_result, "quantum_processor")
        assert analysis["node_type"] == "quantum_processor"
        assert "quantum_state" in analysis["handle_candidates"]


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
