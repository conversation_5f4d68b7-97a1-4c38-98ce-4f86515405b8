"""
Unit tests for semantic type extraction utility.

Tests the semantic_type_extractor module functions for extracting semantic types
from field definitions and handling various edge cases.
"""

import pytest
from unittest.mock import patch, MagicMock

from app.utils.semantic_type_extractor import (
    extract_semantic_type,
    extract_semantic_type_for_nested,
    get_supported_semantic_types,
    validate_semantic_type,
    normalize_semantic_type,
)


class TestExtractSemanticType:
    """Test cases for extract_semantic_type function."""

    def test_extract_semantic_type_email(self):
        """Test extraction of email semantic type."""
        field_def = {
            "field_name": "user_email",
            "data_type": {"type": "string", "format": "email"}
        }
        result = extract_semantic_type(field_def)
        assert result == "email"

    def test_extract_semantic_type_url(self):
        """Test extraction of url semantic type."""
        field_def = {
            "field_name": "website_url",
            "data_type": {"type": "string", "format": "url"}
        }
        result = extract_semantic_type(field_def)
        assert result == "url"

    def test_extract_semantic_type_datetime(self):
        """Test extraction of datetime semantic type."""
        field_def = {
            "field_name": "created_at",
            "data_type": {"type": "string", "format": "datetime"}
        }
        result = extract_semantic_type(field_def)
        assert result == "datetime"

    def test_extract_semantic_type_currency(self):
        """Test extraction of currency semantic type."""
        field_def = {
            "field_name": "price",
            "data_type": {"type": "number", "format": "currency"}
        }
        result = extract_semantic_type(field_def)
        assert result == "currency"

    def test_extract_semantic_type_empty_format(self):
        """Test default return when format is empty."""
        field_def = {
            "field_name": "description",
            "data_type": {"type": "string", "format": ""}
        }
        result = extract_semantic_type(field_def)
        assert result == "string"

    def test_extract_semantic_type_missing_format(self):
        """Test default return when format is missing."""
        field_def = {
            "field_name": "description",
            "data_type": {"type": "string"}
        }
        result = extract_semantic_type(field_def)
        assert result == "string"

    def test_extract_semantic_type_none_format(self):
        """Test default return when format is None."""
        field_def = {
            "field_name": "description",
            "data_type": {"type": "string", "format": None}
        }
        result = extract_semantic_type(field_def)
        assert result == "string"

    def test_extract_semantic_type_whitespace_format(self):
        """Test default return when format is only whitespace."""
        field_def = {
            "field_name": "description",
            "data_type": {"type": "string", "format": "   "}
        }
        result = extract_semantic_type(field_def)
        assert result == "string"

    def test_extract_semantic_type_case_insensitive(self):
        """Test that format extraction is case insensitive."""
        field_def = {
            "field_name": "user_email",
            "data_type": {"type": "string", "format": "EMAIL"}
        }
        result = extract_semantic_type(field_def)
        assert result == "email"

    def test_extract_semantic_type_invalid_field_definition(self):
        """Test handling of invalid field definition."""
        result = extract_semantic_type("invalid")
        assert result == "string"

    def test_extract_semantic_type_missing_data_type(self):
        """Test handling when data_type is missing."""
        field_def = {"field_name": "test_field"}
        result = extract_semantic_type(field_def)
        assert result == "string"

    def test_extract_semantic_type_invalid_data_type(self):
        """Test handling when data_type is not a dict."""
        field_def = {
            "field_name": "test_field",
            "data_type": "invalid"
        }
        result = extract_semantic_type(field_def)
        assert result == "string"

    @patch('app.utils.semantic_type_extractor.logger')
    def test_extract_semantic_type_logging(self, mock_logger):
        """Test that appropriate logging occurs."""
        field_def = {
            "field_name": "user_email",
            "data_type": {"type": "string", "format": "email"}
        }
        extract_semantic_type(field_def)
        mock_logger.debug.assert_called_with("Extracted semantic type 'email' from format field")


class TestExtractSemanticTypeForNested:
    """Test cases for extract_semantic_type_for_nested function."""

    def test_extract_semantic_type_for_array_with_item_format(self):
        """Test extraction for array with item format."""
        field_def = {
            "field_name": "email_list",
            "data_type": {
                "type": "array",
                "items": {"type": "string", "format": "email"}
            }
        }
        result = extract_semantic_type_for_nested(field_def, "array")
        assert result == "array_of_email"

    def test_extract_semantic_type_for_array_with_array_format(self):
        """Test extraction for array with array-level format."""
        field_def = {
            "field_name": "url_list",
            "data_type": {
                "type": "array",
                "format": "url_collection"
            }
        }
        result = extract_semantic_type_for_nested(field_def, "array")
        assert result == "url_collection"

    def test_extract_semantic_type_for_array_no_format(self):
        """Test extraction for array without format."""
        field_def = {
            "field_name": "string_list",
            "data_type": {"type": "array"}
        }
        result = extract_semantic_type_for_nested(field_def, "array")
        assert result == "array"

    def test_extract_semantic_type_for_object_with_format(self):
        """Test extraction for object with format."""
        field_def = {
            "field_name": "user_profile",
            "data_type": {
                "type": "object",
                "format": "user_data"
            }
        }
        result = extract_semantic_type_for_nested(field_def, "object")
        assert result == "user_data"

    def test_extract_semantic_type_for_object_no_format(self):
        """Test extraction for object without format."""
        field_def = {
            "field_name": "user_profile",
            "data_type": {"type": "object"}
        }
        result = extract_semantic_type_for_nested(field_def, "object")
        assert result == "object"

    def test_extract_semantic_type_for_other_types(self):
        """Test extraction for non-array/object types falls back to standard extraction."""
        field_def = {
            "field_name": "user_email",
            "data_type": {"type": "string", "format": "email"}
        }
        result = extract_semantic_type_for_nested(field_def, "string")
        assert result == "email"

    def test_extract_semantic_type_for_nested_invalid_input(self):
        """Test handling of invalid input."""
        result = extract_semantic_type_for_nested("invalid", "array")
        assert result == "string"


class TestGetSupportedSemanticTypes:
    """Test cases for get_supported_semantic_types function."""

    def test_get_supported_semantic_types_structure(self):
        """Test that supported types are returned in correct structure."""
        result = get_supported_semantic_types()
        assert isinstance(result, dict)
        assert "communication" in result
        assert "datetime" in result
        assert "media" in result
        assert "system" in result
        assert "numeric" in result
        assert "default" in result

    def test_get_supported_semantic_types_content(self):
        """Test that expected semantic types are included."""
        result = get_supported_semantic_types()
        assert "email" in result["communication"]
        assert "url" in result["communication"]
        assert "datetime" in result["datetime"]
        assert "currency" in result["numeric"]
        assert "string" in result["default"]


class TestValidateSemanticType:
    """Test cases for validate_semantic_type function."""

    def test_validate_semantic_type_valid_types(self):
        """Test validation of valid semantic types."""
        assert validate_semantic_type("email") is True
        assert validate_semantic_type("url") is True
        assert validate_semantic_type("datetime") is True
        assert validate_semantic_type("currency") is True

    def test_validate_semantic_type_invalid_types(self):
        """Test validation of invalid semantic types."""
        assert validate_semantic_type("invalid_type") is False
        assert validate_semantic_type("") is False

    def test_validate_semantic_type_case_insensitive(self):
        """Test that validation is case insensitive."""
        assert validate_semantic_type("EMAIL") is True
        assert validate_semantic_type("Url") is True

    def test_validate_semantic_type_non_string(self):
        """Test validation with non-string input."""
        assert validate_semantic_type(123) is False
        assert validate_semantic_type(None) is False
        assert validate_semantic_type([]) is False


class TestNormalizeSemanticType:
    """Test cases for normalize_semantic_type function."""

    def test_normalize_semantic_type_aliases(self):
        """Test normalization of semantic type aliases."""
        assert normalize_semantic_type("uri") == "url"
        assert normalize_semantic_type("href") == "url"
        assert normalize_semantic_type("link") == "url"
        assert normalize_semantic_type("mail") == "email"
        assert normalize_semantic_type("e-mail") == "email"
        assert normalize_semantic_type("money") == "currency"

    def test_normalize_semantic_type_case_handling(self):
        """Test case normalization."""
        assert normalize_semantic_type("EMAIL") == "email"
        assert normalize_semantic_type("URL") == "url"

    def test_normalize_semantic_type_whitespace(self):
        """Test whitespace handling."""
        assert normalize_semantic_type("  email  ") == "email"
        assert normalize_semantic_type("\turl\n") == "url"

    def test_normalize_semantic_type_no_alias(self):
        """Test types without aliases."""
        assert normalize_semantic_type("datetime") == "datetime"
        assert normalize_semantic_type("currency") == "currency"

    def test_normalize_semantic_type_invalid_input(self):
        """Test handling of invalid input."""
        assert normalize_semantic_type(123) == "string"
        assert normalize_semantic_type(None) == "string"
        assert normalize_semantic_type([]) == "string"
