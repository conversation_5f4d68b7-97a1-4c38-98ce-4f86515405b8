#!/usr/bin/env python3
"""
Simple test to verify AgenticAI component inputs after removing id and name fields.
"""

import sys
import os

# Add the workflow-service to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'workflow-service'))

try:
    from app.components.ai.agentic_ai import AgenticAI
except ImportError as e:
    print(f"Import error: {e}")
    sys.exit(1)


def test_component_inputs():
    """Test that the component inputs are correct after removing id and name."""
    print("Testing AgenticAI Component Inputs...")
    
    try:
        component = AgenticAI()
        
        # Get all input names
        input_names = [inp.name for inp in component.inputs]
        
        print(f"\n📝 Current Input Fields ({len(input_names)}):")
        for i, name in enumerate(input_names, 1):
            print(f"  {i:2d}. {name}")
        
        # Check that id and name are NOT present
        removed_fields = ["id", "name"]
        found_removed = [field for field in removed_fields if field in input_names]
        
        if found_removed:
            print(f"\n❌ FAILED: These fields should have been removed: {found_removed}")
            return False
        else:
            print(f"\n✅ CONFIRMED: Removed fields are not present: {removed_fields}")
        
        # Check that required fields are present
        required_fields = [
            "description", "execution_type", "query", "system_message", 
            "termination_condition", "max_tokens", "input_variables", 
            "tools", "memory", "autogen_agent_type", "stream"
        ]
        
        missing_fields = [field for field in required_fields if field not in input_names]
        
        if missing_fields:
            print(f"\n❌ FAILED: Missing required fields: {missing_fields}")
            return False
        else:
            print(f"\n✅ CONFIRMED: All required fields are present")
        
        # Check inherited fields from BaseAgentComponent
        inherited_fields = ["model_provider", "base_url", "api_key", "model_name", "temperature"]
        missing_inherited = [field for field in inherited_fields if field not in input_names]
        
        if missing_inherited:
            print(f"\n❌ FAILED: Missing inherited fields: {missing_inherited}")
            return False
        else:
            print(f"\n✅ CONFIRMED: All inherited fields are present")
        
        # Test component properties
        print(f"\n📋 Component Properties:")
        print(f"  Name: {component.name}")
        print(f"  Display Name: {component.display_name}")
        print(f"  Agent Type: {component.agent_type}")
        print(f"  Category: {component.category}")
        
        if component.agent_type != "component":
            print(f"\n❌ FAILED: agent_type should be 'component', got '{component.agent_type}'")
            return False
        
        print(f"\n✅ PASSED: All component input tests successful")
        return True
        
    except Exception as e:
        print(f"\n❌ FAILED: Exception during testing: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_input_field_properties():
    """Test specific properties of key input fields."""
    print("\nTesting Input Field Properties...")
    
    try:
        component = AgenticAI()
        
        # Create a lookup for inputs by name
        inputs_by_name = {inp.name: inp for inp in component.inputs}
        
        # Test execution_type field
        if "execution_type" in inputs_by_name:
            exec_type = inputs_by_name["execution_type"]
            if exec_type.input_type != "dropdown":
                print(f"❌ FAILED: execution_type should be dropdown, got {exec_type.input_type}")
                return False
            if "response" not in exec_type.options or "interactive" not in exec_type.options:
                print(f"❌ FAILED: execution_type missing required options, got {exec_type.options}")
                return False
            print("✅ execution_type field configured correctly")
        
        # Test query field
        if "query" in inputs_by_name:
            query = inputs_by_name["query"]
            if not query.required:
                print("❌ FAILED: query field should be required")
                return False
            if not query.is_handle:
                print("❌ FAILED: query field should be a handle (dual-purpose)")
                return False
            print("✅ query field configured correctly")
        
        # Test termination_condition field
        if "termination_condition" in inputs_by_name:
            term_cond = inputs_by_name["termination_condition"]
            if not hasattr(term_cond, 'visibility_rules') or not term_cond.visibility_rules:
                print("❌ FAILED: termination_condition should have visibility rules")
                return False
            print("✅ termination_condition field configured correctly")
        
        # Test max_tokens field
        if "max_tokens" in inputs_by_name:
            max_tokens = inputs_by_name["max_tokens"]
            if max_tokens.input_type != "int":
                print(f"❌ FAILED: max_tokens should be int type, got {max_tokens.input_type}")
                return False
            if max_tokens.value != 1000:
                print(f"❌ FAILED: max_tokens default should be 1000, got {max_tokens.value}")
                return False
            print("✅ max_tokens field configured correctly")
        
        print("\n✅ PASSED: All input field property tests successful")
        return True
        
    except Exception as e:
        print(f"\n❌ FAILED: Exception during field property testing: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    print("AgenticAI Component Input Validation")
    print("=" * 50)
    
    tests = [
        test_component_inputs,
        test_input_field_properties,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ FAILED: {test.__name__} - Exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed!")
        print("✅ Component inputs are correctly configured")
        print("✅ No gRPC proto changes needed")
        print("\n📋 Summary:")
        print("  - 'id' and 'name' fields successfully removed from user inputs")
        print("  - All required fields are present and properly configured")
        print("  - Component will work correctly with existing gRPC infrastructure")
        return 0
    else:
        print("\n⚠️  Some tests failed. Please review the implementation.")
        return 1


if __name__ == "__main__":
    exit(main())
