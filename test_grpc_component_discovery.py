#!/usr/bin/env python3
"""
Test script to verify that the gRPC component discovery works correctly
after removing id and name fields from AgenticAI component inputs.
"""

import sys
import os
import asyncio
from typing import Dict, Any

# Add the workflow-service to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'workflow-service'))

try:
    from app.components.ai.agentic_ai import AgenticAI
    from app.services.workflow_builder.enhanced_component_service import EnhancedComponentService
    from app.services.workflow_builder.workflow_builder_service import WorkflowBuilderService
    from app.grpc_.workflow_pb2 import DiscoverComponentsRequest
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the correct directory")
    sys.exit(1)


async def test_component_discovery():
    """Test that component discovery works correctly via the service layer."""
    print("Testing Component Discovery Service...")
    
    try:
        # Test the enhanced component service
        service = EnhancedComponentService()
        components = await service.discover_components(force_refresh=True)
        
        # Check if AgenticAI is discovered
        if "AI" not in components:
            print("❌ FAILED: AI category not found in discovered components")
            return False
            
        if "AgenticAI" not in components["AI"]:
            print("❌ FAILED: AgenticAI component not found in AI category")
            return False
            
        agentic_ai = components["AI"]["AgenticAI"]
        
        print(f"\n📋 AgenticAI Component Discovery Results:")
        print(f"Name: {agentic_ai.name}")
        print(f"Display Name: {agentic_ai.display_name}")
        print(f"Description: {agentic_ai.description}")
        print(f"Category: {agentic_ai.category}")
        print(f"Input Count: {len(agentic_ai.inputs)}")
        
        # Check that id and name are NOT in the inputs
        input_names = [inp.name for inp in agentic_ai.inputs]
        print(f"\n📝 Input Fields: {input_names}")
        
        if "id" in input_names:
            print("❌ FAILED: 'id' field should not be in component inputs")
            return False
            
        if "name" in input_names:
            print("❌ FAILED: 'name' field should not be in component inputs")
            return False
            
        # Check that required fields are present
        required_fields = ["description", "execution_type", "query", "max_tokens"]
        missing_fields = [field for field in required_fields if field not in input_names]
        
        if missing_fields:
            print(f"❌ FAILED: Missing required input fields: {missing_fields}")
            return False
            
        print("✅ PASSED: Component discovery service test")
        return True
        
    except Exception as e:
        print(f"❌ FAILED: Component discovery service test - Exception: {e}")
        return False


def test_grpc_conversion():
    """Test that the gRPC conversion works correctly."""
    print("\nTesting gRPC Proto Conversion...")
    
    try:
        # Create a mock component service
        service = WorkflowBuilderService()
        
        # Create a discover components request
        request = DiscoverComponentsRequest(force_refresh=True)
        
        # Call the gRPC method
        response = service.discoverComponents(request, None)
        
        # Find the AgenticAI component in the response
        agentic_ai_component = None
        for component in response.components:
            if component.name == "AgenticAI":
                agentic_ai_component = component
                break
                
        if not agentic_ai_component:
            print("❌ FAILED: AgenticAI component not found in gRPC response")
            return False
            
        print(f"\n📋 AgenticAI gRPC Component:")
        print(f"ID: {agentic_ai_component.id}")
        print(f"Name: {agentic_ai_component.name}")
        print(f"Display Name: {agentic_ai_component.display_name}")
        print(f"Category: {agentic_ai_component.category}")
        print(f"Input Count: {len(agentic_ai_component.inputs)}")
        
        # Check input fields in gRPC response
        grpc_input_names = [inp.name for inp in agentic_ai_component.inputs]
        print(f"\n📝 gRPC Input Fields: {grpc_input_names}")
        
        if "id" in grpc_input_names:
            print("❌ FAILED: 'id' field should not be in gRPC component inputs")
            return False
            
        if "name" in grpc_input_names:
            print("❌ FAILED: 'name' field should not be in gRPC component inputs")
            return False
            
        # Check that required fields are present in gRPC response
        required_fields = ["description", "execution_type", "query", "max_tokens"]
        missing_fields = [field for field in required_fields if field not in grpc_input_names]
        
        if missing_fields:
            print(f"❌ FAILED: Missing required input fields in gRPC response: {missing_fields}")
            return False
            
        # Verify specific field properties
        for inp in agentic_ai_component.inputs:
            if inp.name == "execution_type":
                if inp.input_type != "dropdown":
                    print(f"❌ FAILED: execution_type should be dropdown, got {inp.input_type}")
                    return False
                if "response" not in inp.options or "interactive" not in inp.options:
                    print(f"❌ FAILED: execution_type should have response/interactive options, got {inp.options}")
                    return False
                    
            elif inp.name == "query":
                if not inp.required:
                    print("❌ FAILED: query field should be required")
                    return False
                    
            elif inp.name == "termination_condition":
                if len(inp.visibility_rules) == 0:
                    print("❌ FAILED: termination_condition should have visibility rules")
                    return False
                    
        print("✅ PASSED: gRPC proto conversion test")
        return True
        
    except Exception as e:
        print(f"❌ FAILED: gRPC proto conversion test - Exception: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_component_instantiation():
    """Test that the component can still be instantiated correctly."""
    print("\nTesting Component Instantiation...")
    
    try:
        component = AgenticAI()
        
        # Test component properties
        if component.name != "AgenticAI":
            print(f"❌ FAILED: Component name should be 'AgenticAI', got '{component.name}'")
            return False
            
        if component.display_name != "AI Agent Executor":
            print(f"❌ FAILED: Display name should be 'AI Agent Executor', got '{component.display_name}'")
            return False
            
        if component.agent_type != "component":
            print(f"❌ FAILED: agent_type should be 'component', got '{component.agent_type}'")
            return False
            
        # Test input definitions
        input_names = [inp.name for inp in component.inputs]
        
        if "id" in input_names or "name" in input_names:
            print("❌ FAILED: id and name should not be in component inputs")
            return False
            
        required_fields = ["description", "execution_type", "query", "max_tokens"]
        missing_fields = [field for field in required_fields if field not in input_names]
        
        if missing_fields:
            print(f"❌ FAILED: Missing required input fields: {missing_fields}")
            return False
            
        print("✅ PASSED: Component instantiation test")
        return True
        
    except Exception as e:
        print(f"❌ FAILED: Component instantiation test - Exception: {e}")
        return False


async def main():
    """Run all tests."""
    print("gRPC Component Discovery Tests")
    print("=" * 60)
    
    tests = [
        test_component_instantiation,
        test_component_discovery,
        test_grpc_conversion,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if asyncio.iscoroutinefunction(test):
                result = await test()
            else:
                result = test()
                
            if result:
                passed += 1
        except Exception as e:
            print(f"❌ FAILED: {test.__name__} - Exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The gRPC component discovery is working correctly.")
        print("✅ No proto file changes are needed.")
        return 0
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
        return 1


if __name__ == "__main__":
    exit(asyncio.run(main()))
