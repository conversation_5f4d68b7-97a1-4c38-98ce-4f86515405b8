{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__1daf8b13._.js", "server/edge/chunks/edge-wrapper_0cc0fab8.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next|_vercel|.*\\.[\\w]+$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next|_vercel|.*\\.[\\w]+$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "a+lIWRKRxvnVQaEcqhJqHOiJoD1n5nRfxss0mxpJlgU=", "__NEXT_PREVIEW_MODE_ID": "48acc3d76280e06b1805178a4fa4d6c8", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "deda24d6969c979e28abf0667903cc51fdb31a5ea8097988333d9f33ce7201e8", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "2122b5882d3ff9fd399c2d1fc6b49f85e6c7b47bc43efed480e35b32bea6faf7"}}}, "sortedMiddleware": ["/"], "functions": {}}