import type { NextConfig } from "next";
import { validateEnv } from './lib/env';

// Validate environment variables
validateEnv();

const securityHeaders = [
  {
    key: 'X-DNS-Prefetch-Control',
    value: 'on'
  },
  {
    key: 'X-XSS-Protection',
    value: '1; mode=block'
  },
  {
    key: 'X-Frame-Options',
    value: 'SAMEORIGIN'
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff'
  },
  {
    key: 'Referrer-Policy',
    value: 'strict-origin-when-cross-origin'
  },
  {
    key: 'Permissions-Policy',
    value: 'camera=(), microphone=(), geolocation=(), interest-cohort=()'
  },
  {
  key: 'Content-Security-Policy',
  value: process.env.NODE_ENV === 'production'
    ? "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self'; connect-src 'self' https://* http://gateway-api-version-integration.rapidinnovation.dev; frame-ancestors 'none';"
    : "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self'; connect-src 'self' http://localhost:* https://* ws://localhost:* https://gateway-api-version-integration.rapidinnovation.dev; frame-ancestors 'none';"
  }

];

const nextConfig: NextConfig = {
  // TypeScript configuration
  typescript: {
    // Don't ignore TypeScript errors during builds
    ignoreBuildErrors: true,
  },

  // ESLint configuration
  eslint: {
    // Keep ignoring ESLint errors during builds for now
    ignoreDuringBuilds: true,
  },

  // React configuration
  reactStrictMode: true, // Enabled React strict mode

  // Security headers
  async headers() {
    return [
      {
        // Apply these headers to all routes
        source: '/:path*',
        headers: securityHeaders,
      },
    ];
  },

  // Caching strategies
  experimental: {
    // Configure caching for dynamic routes
    staleTimes: {
      // Default stale time for dynamic routes (in seconds)
      dynamic: 30,
    },
  },

  // Bundling options
  bundlePagesRouterDependencies: true,
  serverExternalPackages: [
    // Add packages that should not be bundled by Next.js
    // Example: 'sharp', 'canvas'
  ],

  // Development indicators
  devIndicators: {
    position: 'bottom-right',
  },
};

export default nextConfig;
