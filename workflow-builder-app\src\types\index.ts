// src/types/index.ts

export interface InputVisibilityRule {
  field_name: string;
  field_value: any;
  operator?:
    | "equals"
    | "not_equals"
    | "contains"
    | "greater_than"
    | "less_than"
    | "exists"
    | "not_exists";
}

export interface InputRequirementRule {
  field_name: string;
  field_value: any;
  operator?:
    | "equals"
    | "not_equals"
    | "contains"
    | "greater_than"
    | "less_than"
    | "exists"
    | "not_exists";
}

// Input definition can specify the logic operator for visibility rules
export type VisibilityLogic = "AND" | "OR";

// Validation object for input validation
export interface InputValidation {
  minLength?: number; // Minimum length for string inputs
  maxLength?: number; // Maximum length for string inputs
  min?: number; // Minimum value for number inputs
  max?: number; // Maximum value for number inputs
  pattern?: string; // Regex pattern for string validation
  required?: boolean; // Whether the input is required
}

// Match the input definition from backend/definitions/inputs.py
export interface InputDefinition {
  name: string;
  display_name: string;
  info?: string | null;
  value?: any;
  options?: Array<string | { value: string; label: string }> | null; // Support both string[] and object[] formats
  real_time_refresh?: boolean;
  advanced?: boolean;
  input_types?: string[] | null;
  required?: boolean;
  is_list?: boolean;
  is_handle?: boolean;
  input_type: string; // e.g., 'string', 'dropdown', 'bool', 'credential'
  // Add specific fields like language, file_types if needed based on input_type
  visibility_rules?: InputVisibilityRule[] | null;
  visibility_logic?: VisibilityLogic;
  requirement_rules?: InputRequirementRule[] | null;
  requirement_logic?: VisibilityLogic;
  language?: string;
  file_types?: string[];
  allow_multiple?: boolean;
  list_item_type?: string;
  // Credential input specific fields
  credential_type?: string; // Type of credential (api_key, oauth, etc.)
  use_credential_id?: boolean; // Whether to use a credential ID or direct input
  credential_id?: string; // ID of the credential in the credential store
  // Dynamic handle input specific fields
  min_handles?: number; // Minimum number of handles
  max_handles?: number; // Maximum number of handles
  default_handles?: number; // Default number of handles
  base_name?: string; // Base name for generated handles
  base_display_name?: string; // Base display name for generated handles
  allow_direct_input?: boolean; // Whether to allow direct input in the inspector panel
  show_add_button?: boolean; // Whether to show a button to add more inputs
  show_remove_button?: boolean; // Whether to show a button to remove inputs
  // Validation fields
  min_length?: number; // Minimum length for string inputs
  max_length?: number; // Maximum length for string inputs
  min_value?: number; // Minimum value for number inputs
  max_value?: number; // Maximum value for number inputs
  min_items?: number; // Minimum number of items for list inputs
  max_items?: number; // Maximum number of items for list inputs
  pattern?: string; // Regex pattern for string validation
  pattern_error?: string; // Error message for pattern validation
  required_keys?: string[]; // Required keys for dictionary inputs
  // For object type inputs with nested properties
  properties?: Record<string, any>; // Properties for object type inputs
  // MCP-specific information
  mcp_info?: MCPSchemaInfo; // Reference to MCP schema information
  // Validation object for more complex validation
  validation?: InputValidation;
}

// Match the output definition from backend/definitions/outputs.py
export interface OutputDefinition {
  name: string;
  display_name: string;
  output_type: string;
  semantic_type?: string | null;
  method?: string | null;
}

// MCP Schema information
export interface MCPSchemaInfo {
  server_id: string;
  server_path: string;
  tool_name: string;
  tool_id?: string;
  endpoint?: string;
  input_schema: any;
  output_schema: any;
}

// Match the component definition from backend BaseNode.get_definition()
export interface ComponentDefinition {
  name: string;
  display_name: string;
  description: string;
  category: string;
  icon: string;
  beta: boolean;
  requires_approval?: boolean; // Whether this component requires approval before execution
  inputs: InputDefinition[];
  outputs: OutputDefinition[];
  is_valid: boolean;
  path: string; // Example: "components.io.inputnode"
  type?: string; // Component type, e.g., "MCPMarketplaceComponent"
  mcp_info?: MCPSchemaInfo; // MCP-specific information
}

// Structure matching the API endpoint /api/components
export interface ComponentsApiResponse {
  [category: string]: {
    [componentName: string]: ComponentDefinition;
  };
}

// Type for nodes used in React Flow
// Add more specific data as needed (like configuration values)
export interface WorkflowNodeData {
  label: string;
  type: string; // Enum value: 'mcp', 'component', or 'agent'
  originalType?: string; // Original component type name (e.g., 'MCPToolsComponent', 'InputNode')
  // Store actual configuration values here, mirroring inputs
  config?: Record<string, any>;
  // Store original definition for reference in inspector
  definition?: ComponentDefinition;
  // Additional properties that might be used in the application
  isValid?: boolean;
  isExecuting?: boolean;
  isComplete?: boolean;
  hasError?: boolean;
  errorMessage?: string;
  outputs?: Record<string, any>;
  // For StartNode specific properties
  collected_parameters?: Record<string, any>;
}

// Extend the Window interface to include our global properties
declare global {
  interface Window {
    startNodeCollectedParameters?: Record<string, any>;
    startNodeCollectedParametersNeedsRefresh?: boolean;
    currentWorkflowNodes?: any[];
  }
}
