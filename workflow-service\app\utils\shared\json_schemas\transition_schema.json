{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://example.com/transition-enhanced.json", "type": "object", "title": "Enhanced Transition Schema", "properties": {"nodes": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "server_script_path": {"type": "string"}, "server_tools": {"type": "array", "items": {"type": "object", "properties": {"tool_id": {"type": "number"}, "tool_name": {"type": "string"}, "input_schema": {"type": "object", "description": "Unified schema for server inputs", "properties": {"predefined_fields": {"type": "array", "description": "Explicitly defined input fields required by the server.", "items": {"type": "object", "properties": {"field_name": {"type": "string", "description": "The name of the input parameter."}, "data_type": {"type": "object", "properties": {"type": {"type": "string", "enum": ["string", "number", "boolean", "array", "object"], "description": "The base type of the field."}, "description": {"type": "string", "description": "Optional description of the field."}, "format": {"type": "string", "description": "Optional format for string types (e.g., 'date', 'email')."}, "items": {"type": "object", "properties": {"type": {"type": "string", "enum": ["string", "number", "boolean", "array", "object"], "description": "The type of items in the array."}, "properties": {"type": "object", "description": "Schema definition for object items.", "additionalProperties": true}, "required": {"type": "array", "items": {"type": "string"}, "description": "Required fields inside the object items."}}, "required": ["type"]}}, "required": ["type"], "description": "The data type of the input parameter, stored as a rich metadata object."}, "required": {"type": "boolean", "default": false, "description": "Indicates whether this field is mandatory."}}, "required": ["field_name", "data_type", "required"]}}}, "required": ["predefined_fields"]}, "output_schema": {"type": "object", "description": "Unified schema for server outputs", "properties": {"predefined_fields": {"type": "array", "description": "Explicitly defined output fields generated by the server.", "items": {"type": "object", "properties": {"field_name": {"type": "string", "description": "The name of the output field."}, "data_type": {"type": "object", "properties": {"type": {"type": "string", "enum": ["string", "number", "boolean", "array", "object"], "description": "The base type of the field."}, "description": {"type": "string", "description": "Optional description of the field."}, "format": {"type": "string", "description": "Optional format for string types (e.g., 'date', 'email')."}, "items": {"type": "object", "properties": {"type": {"type": "string", "enum": ["string", "number", "boolean", "array", "object"], "description": "The type of items in the array."}, "properties": {"type": "object", "description": "Schema definition for object items.", "additionalProperties": true}, "required": {"type": "array", "items": {"type": "string"}, "description": "Required fields inside the object items."}}, "required": ["type"]}}, "required": ["type"], "description": "The data type of the input parameter, stored as a rich metadata object."}}, "required": ["field_name", "data_type"]}}}, "required": ["predefined_fields"]}}, "required": ["tool_id", "tool_name", "input_schema", "output_schema"]}}}, "required": ["id", "server_script_path", "server_tools"]}}, "transitions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "sequence": {"type": "integer"}, "transition_type": {"type": "string", "enum": ["initial", "standard", "reflection"]}, "execution_type": {"type": "string", "description": "Specifies the type of transition execution (e.g., MCP server, API request, Database Operation, Agent execution).", "enum": ["MCP", "Components", "agent"]}, "node_info": {"type": "object", "properties": {"node_id": {"type": "string"}, "tools_to_use": {"type": "array", "items": {"type": "object", "properties": {"tool_id": {"type": "number"}, "tool_name": {"type": "string"}, "tool_params": {"type": "object", "description": "Parameters to invoke the tool with", "properties": {"items": {"type": "array", "description": "List of tool parameters", "items": {"type": "object", "properties": {"field_name": {"type": "string", "description": "The name of the input parameter."}, "data_type": {"type": "string", "enum": ["string", "number", "boolean", "array", "object"]}, "field_value": {"description": "The actual value of the input parameter.", "oneOf": [{"type": "string"}, {"type": "number"}, {"type": "boolean"}, {"type": "array"}, {"type": "null"}, {"type": "object"}]}}, "required": ["field_name", "data_type", "field_value"]}}}, "required": ["items"]}}, "required": ["tool_id", "tool_name"]}}, "input_data": {"type": "array", "description": "Handle-based input data mappings for this transition", "items": {"type": "object", "properties": {"from_transition_id": {"type": "string", "description": "ID of the source transition providing the data"}, "source_node_id": {"type": "string", "description": "Source node ID for data routing and identification"}, "data_type": {"type": "string", "description": "Expected data type for validation"}, "handle_mappings": {"type": "array", "description": "Direct handle-to-handle mappings for data propagation", "items": {"type": "object", "properties": {"source_transition_id": {"type": "string", "description": "ID of the source transition (same as parent from_transition_id)"}, "source_handle_id": {"type": "string", "description": "Exact handle ID from source node output (from edge.sourceHandle)"}, "target_handle_id": {"type": "string", "description": "Exact handle ID for target node input (from edge.targetHandle)"}, "edge_id": {"type": "string", "description": "Reference to original workflow edge for traceability"}}, "required": ["source_transition_id", "source_handle_id", "target_handle_id", "edge_id"]}}}, "required": ["from_transition_id", "source_node_id", "data_type", "handle_mappings"]}}, "output_data": {"type": "array", "description": "Handle-based output data routing configurations for this transition", "items": {"type": "object", "properties": {"to_transition_id": {"type": "string", "description": "ID of the target transition that will receive this data"}, "target_node_id": {"type": "string", "description": "Target node ID for output data routing"}, "data_type": {"type": "string", "description": "Data type of the output data"}, "output_handle_registry": {"type": "object", "description": "Registry mapping handle IDs to their result extraction paths", "properties": {"handle_mappings": {"type": "array", "description": "Mappings from handle IDs to result paths", "items": {"type": "object", "properties": {"handle_id": {"type": "string", "description": "Output handle ID (from edge.sourceHandle)"}, "result_path": {"type": "string", "description": "Path to extract data from node result (e.g., 'result', 'output_data', 'result.result')"}, "edge_id": {"type": "string", "description": "Reference to workflow edge for traceability"}}, "required": ["handle_id", "result_path", "edge_id"]}}}, "required": ["handle_mappings"]}}, "required": ["to_transition_id", "target_node_id", "data_type", "output_handle_registry"]}}}, "required": ["node_id", "tools_to_use", "input_data", "output_data"]}, "result_resolution": {"type": "object", "description": "Universal result resolution metadata for dynamic data extraction", "properties": {"node_type": {"type": "string", "description": "Type of the node (mcp, component, api, custom, etc.)"}, "expected_result_structure": {"type": "string", "enum": ["direct", "nested_result", "nested_output", "dynamic"], "description": "Expected result structure pattern: direct (field at root), nested_result (under 'result'), nested_output (under 'output_data'), dynamic (discover at runtime)"}, "handle_registry": {"type": "object", "description": "Registry of all handles for this transition with their metadata", "properties": {"input_handles": {"type": "array", "description": "All input handles for this transition", "items": {"type": "object", "properties": {"handle_id": {"type": "string", "description": "Input handle ID"}, "handle_name": {"type": "string", "description": "Human-readable handle name"}, "data_type": {"type": "string", "description": "Expected data type for this handle"}, "required": {"type": "boolean", "description": "Whether this handle connection is required"}}, "required": ["handle_id", "handle_name", "data_type", "required"]}}, "output_handles": {"type": "array", "description": "All output handles for this transition", "items": {"type": "object", "properties": {"handle_id": {"type": "string", "description": "Output handle ID"}, "handle_name": {"type": "string", "description": "Human-readable handle name"}, "data_type": {"type": "string", "description": "Data type this handle will output"}, "result_path_hint": {"type": "string", "description": "Hint for where to find this handle's data in the result (can be overridden by dynamic discovery)"}}, "required": ["handle_id", "handle_name", "data_type"]}}}, "required": ["input_handles", "output_handles"]}, "dynamic_discovery": {"type": "object", "description": "Configuration for dynamic result structure discovery", "properties": {"enabled": {"type": "boolean", "description": "Whether to enable dynamic discovery for unknown result structures"}, "fallback_patterns": {"type": "array", "description": "Fallback patterns to try when dynamic discovery is needed", "items": {"type": "string", "description": "Result path pattern (e.g., 'result', 'output_data', 'result.result', '{handle_id}')"}}, "validation_rules": {"type": "array", "description": "Rules to validate discovered result paths", "items": {"type": "object", "properties": {"rule_type": {"type": "string", "enum": ["type_check", "structure_check", "content_check"], "description": "Type of validation rule"}, "rule_config": {"type": "object", "description": "Configuration for the validation rule"}}, "required": ["rule_type", "rule_config"]}}}, "required": ["enabled"]}}, "required": ["node_type", "expected_result_structure", "handle_registry", "dynamic_discovery"]}, "reflection": {"type": "object", "properties": {"iteration_count": {"type": "integer"}, "max_iterations": {"type": "integer"}}, "required": ["iteration_count", "max_iterations"]}, "conditional_routing": {"type": "object", "properties": {"global_context_definitions": {"type": "object", "description": "Defines the global context variables used in this transition's conditions", "properties": {"variables": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the global context variable"}, "data_type": {"type": "string", "enum": ["string", "number", "boolean", "array", "object"]}, "value": {"oneOf": [{"type": "string"}, {"type": "number"}, {"type": "boolean"}, {"type": "array"}, {"type": "object"}, {"type": "null"}]}}, "required": ["name", "data_type", "value"]}}}}, "cases": {"type": "array", "items": {"type": "object", "properties": {"condition": {"type": "object", "properties": {"source": {"type": "string", "enum": ["node_output", "global_context"]}, "variable_name": {"type": "string", "description": "Must match a name defined in global_context_definitions if source is 'global_context'"}, "operator": {"type": "string", "enum": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty", "ai_evaluate"]}, "expected_value": {"oneOf": [{"type": "string"}, {"type": "number"}, {"type": "boolean"}, {"type": "array"}, {"type": "object"}, {"type": "null"}]}}, "required": ["source", "operator"]}, "next_transition": {"type": "string"}}, "required": ["condition", "next_transition"]}}}, "required": ["cases"]}, "end": {"type": "boolean"}}, "required": ["id", "sequence", "transition_type", "execution_type", "node_info", "result_resolution", "end"]}}}, "required": ["nodes", "transitions"]}